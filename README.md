# Math Explainer Platform

A comprehensive mathematical problem-solving platform that provides detailed, step-by-step solutions for algebra, calculus, and statistics problems with interactive visualizations.

## Features

- **Symbolic Math Processing**: Powered by SymPy for accurate mathematical computations
- **Step-by-Step Solutions**: Detailed explanations for each mathematical transformation
- **Interactive Visualizations**: Dynamic graphs and plots using <PERSON><PERSON><PERSON> and Matplotlib
- **Problem Parsing**: Intelligent interpretation of mathematical expressions and natural language
- **Multiple Math Domains**: Support for algebra, calculus, statistics, and more
- **Web Interface**: User-friendly Streamlit-based interface

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the Streamlit application:
```bash
streamlit run app.py
```

## Project Structure

- `app.py` - Main Streamlit application
- `math_engine/` - Core mathematical processing modules
- `parsers/` - Problem parsing and interpretation
- `visualizers/` - Plotting and visualization components
- `tests/` - Test suite for validation

## Supported Problem Types

- Algebraic equations and inequalities
- <PERSON><PERSON> (derivatives, integrals, limits)
- Statistical analysis and distributions
- Trigonometric functions
- Linear algebra operations
