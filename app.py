"""
Math Explainer Platform - Main Streamlit Application

A comprehensive mathematical problem-solving platform with step-by-step solutions,
interactive visualizations, and detailed explanations.
"""

import streamlit as st
import sympy as sp
import numpy as np
import pandas as pd
from typing import Dict, Any, List

# Import custom modules
from math_engine import AlgebraSolver, CalculusSolver, StatisticsSolver, SolutionFormatter
from parsers import MathParser, ProblemClassifier, NaturalLanguageParser
from visualizers import FunctionPlotter, StatisticsVisualizer

# Page configuration
st.set_page_config(
    page_title="Math Explainer Platform",
    page_icon="🧮",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .step-container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
        border-left: 4px solid #1f77b4;
    }
    .solution-step {
        margin: 0.5rem 0;
    }
    .error-message {
        background-color: #ffe6e6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ff4444;
    }
    .success-message {
        background-color: #e6ffe6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #44ff44;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if 'solution_history' not in st.session_state:
        st.session_state.solution_history = []
    if 'current_solution' not in st.session_state:
        st.session_state.current_solution = None
    if 'math_parser' not in st.session_state:
        st.session_state.math_parser = MathParser()
    if 'problem_classifier' not in st.session_state:
        st.session_state.problem_classifier = ProblemClassifier()
    if 'nl_parser' not in st.session_state:
        st.session_state.nl_parser = NaturalLanguageParser()
    if 'solution_formatter' not in st.session_state:
        st.session_state.solution_formatter = SolutionFormatter()

def main():
    """Main application function."""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🧮 Math Explainer Platform</h1>', unsafe_allow_html=True)
    st.markdown("**Solve mathematical problems with detailed step-by-step explanations and interactive visualizations**")
    
    # Sidebar for navigation and settings
    with st.sidebar:
        st.header("Navigation")
        page = st.selectbox(
            "Choose a section:",
            ["Problem Solver", "Function Plotter", "Statistics Tools", "Solution History", "Help & Examples"]
        )
        
        st.header("Settings")
        show_steps = st.checkbox("Show detailed steps", value=True)
        show_plots = st.checkbox("Show visualizations", value=True)
        latex_rendering = st.checkbox("Render LaTeX", value=True)
    
    # Main content based on selected page
    if page == "Problem Solver":
        problem_solver_page(show_steps, show_plots, latex_rendering)
    elif page == "Function Plotter":
        function_plotter_page()
    elif page == "Statistics Tools":
        statistics_tools_page()
    elif page == "Solution History":
        solution_history_page()
    elif page == "Help & Examples":
        help_examples_page()

def problem_solver_page(show_steps: bool, show_plots: bool, latex_rendering: bool):
    """Main problem solver interface."""
    st.header("Mathematical Problem Solver")
    
    # Input methods
    input_method = st.radio(
        "Choose input method:",
        ["Mathematical Expression", "Natural Language", "Multiple Equations"]
    )
    
    if input_method == "Mathematical Expression":
        problem_input = st.text_area(
            "Enter your mathematical problem:",
            placeholder="e.g., x^2 + 2*x - 3 = 0, or d/dx(x^2 + sin(x)), or integrate x^2 dx",
            height=100
        )
        
    elif input_method == "Natural Language":
        problem_input = st.text_area(
            "Describe your problem in plain English:",
            placeholder="e.g., Find the derivative of x squared plus sine of x, or Solve for x when x squared plus 2x equals 3",
            height=100
        )
        
    elif input_method == "Multiple Equations":
        st.write("Enter system of equations (one per line):")
        eq1 = st.text_input("Equation 1:", placeholder="e.g., 2*x + 3*y = 7")
        eq2 = st.text_input("Equation 2:", placeholder="e.g., x - y = 1")
        eq3 = st.text_input("Equation 3 (optional):", placeholder="")
        
        equations = [eq for eq in [eq1, eq2, eq3] if eq.strip()]
        problem_input = "\n".join(equations)
    
    # Solve button
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        solve_button = st.button("🔍 Solve Problem", type="primary", use_container_width=True)
    
    if solve_button and problem_input.strip():
        with st.spinner("Analyzing and solving your problem..."):
            solve_problem(problem_input, input_method, show_steps, show_plots, latex_rendering)
    
    # Display current solution if available
    if st.session_state.current_solution:
        display_solution(st.session_state.current_solution, show_steps, show_plots, latex_rendering)

def solve_problem(problem_input: str, input_method: str, show_steps: bool, show_plots: bool, latex_rendering: bool):
    """Solve the mathematical problem."""
    try:
        # Parse the input based on method
        if input_method == "Natural Language":
            # Parse natural language first
            nl_result = st.session_state.nl_parser.parse_natural_language(problem_input)
            if nl_result['success']:
                math_expression = nl_result['math_expression']
                parsed_result = st.session_state.math_parser.parse_expression(math_expression)
            else:
                st.error("Could not parse natural language input. Please try mathematical notation.")
                return
        elif input_method == "Multiple Equations":
            equations = [eq.strip() for eq in problem_input.split('\n') if eq.strip()]
            parsed_result = st.session_state.math_parser.parse_system(equations)
        else:
            # Direct mathematical expression
            if '=' in problem_input:
                parsed_result = st.session_state.math_parser.parse_equation(problem_input)
            else:
                parsed_result = st.session_state.math_parser.parse_expression(problem_input)
        
        if not parsed_result['success']:
            st.error(f"Parsing error: {parsed_result.get('error', 'Unknown error')}")
            return
        
        # Classify the problem
        classification = st.session_state.problem_classifier.classify_problem(problem_input, parsed_result)
        
        # Solve based on classification
        solution = solve_based_on_classification(classification, parsed_result)
        
        # Format solution
        formatted_solution = st.session_state.solution_formatter.format_solution_summary(solution)
        
        # Store in session state
        st.session_state.current_solution = {
            'input': problem_input,
            'input_method': input_method,
            'parsed': parsed_result,
            'classification': classification,
            'solution': solution,
            'formatted': formatted_solution
        }
        
        # Add to history
        st.session_state.solution_history.append(st.session_state.current_solution)
        
    except Exception as e:
        st.error(f"An error occurred while solving: {str(e)}")

def solve_based_on_classification(classification: Dict[str, Any], parsed_result: Dict[str, Any]) -> Dict[str, Any]:
    """Solve problem based on its classification."""
    approach = classification['solving_approach']
    solver_name = approach.get('solver', 'AlgebraSolver')
    method_name = approach.get('method', 'solve_equation')
    parameters = approach.get('parameters', {})
    
    # Initialize appropriate solver
    if solver_name == 'AlgebraSolver':
        solver = AlgebraSolver()
    elif solver_name == 'CalculusSolver':
        solver = CalculusSolver()
    elif solver_name == 'StatisticsSolver':
        solver = StatisticsSolver()
    else:
        solver = AlgebraSolver()  # Default
    
    # Call the appropriate method
    if hasattr(solver, method_name):
        method = getattr(solver, method_name)
        
        # Prepare parameters
        if method_name == 'solve_equation' and 'equation_str' not in parameters:
            parameters['equation_str'] = parsed_result.get('original', '')
        elif method_name == 'solve_system' and 'equations' not in parameters:
            parameters['equations'] = parsed_result.get('original_equations', [])
        
        return method(**parameters)
    else:
        return {'error': f'Method {method_name} not found in {solver_name}', 'success': False}

def display_solution(solution_data: Dict[str, Any], show_steps: bool, show_plots: bool, latex_rendering: bool):
    """Display the solution with formatting."""
    st.header("Solution")
    
    formatted = solution_data['formatted']
    
    # Display problem type and classification
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**Problem Type:** {formatted['problem_type']}")
    with col2:
        confidence = solution_data['classification']['confidence']
        st.info(f"**Classification Confidence:** {confidence:.1%}")
    
    # Display final result
    if formatted['success']:
        st.markdown(f'<div class="success-message"><strong>Final Answer:</strong> {formatted["final_result"]}</div>', 
                   unsafe_allow_html=True)
    else:
        st.markdown(f'<div class="error-message"><strong>Error:</strong> {formatted.get("error_message", "Unknown error")}</div>', 
                   unsafe_allow_html=True)
        return
    
    # Display steps if requested
    if show_steps and formatted['formatted_steps']:
        st.subheader("Step-by-Step Solution")
        
        for step in formatted['formatted_steps']:
            with st.expander(f"Step {step['step_number']}: {step['title']}", expanded=True):
                if latex_rendering and step['formatted_latex']:
                    st.latex(step['formatted_latex'])
                else:
                    st.code(str(step['expression']))
                
                if step['explanation']:
                    st.write(step['explanation'])
    
    # Display visualizations if requested
    if show_plots:
        display_visualizations(solution_data)

def display_visualizations(solution_data: Dict[str, Any]):
    """Display appropriate visualizations based on problem type."""
    classification = solution_data['classification']
    problem_type = classification['problem_type']
    solution = solution_data['solution']
    
    if not solution.get('success'):
        return
    
    st.subheader("Visualizations")
    
    try:
        if 'algebra' in problem_type.value:
            # Plot the equation or function
            plotter = FunctionPlotter()
            
            if 'solutions' in solution:
                # Plot the original equation and mark solutions
                original_expr = solution_data['parsed'].get('original', '')
                if '=' in original_expr:
                    left_side = original_expr.split('=')[0].strip()
                    plot_result = plotter.plot_function(left_side, title=f"Function: {left_side}")
                    
                    if plot_result['success']:
                        st.plotly_chart(plot_result['figure'], use_container_width=True)
        
        elif 'calculus' in problem_type.value:
            plotter = FunctionPlotter()
            
            if 'result' in solution:
                original_expr = solution_data['parsed'].get('original', '')
                
                if 'derivative' in problem_type.value:
                    # Plot function and its derivative
                    plot_result = plotter.plot_derivative_comparison(original_expr)
                    if plot_result['success']:
                        st.plotly_chart(plot_result['figure'], use_container_width=True)
                
                elif 'integral' in problem_type.value:
                    # Plot function and highlight area under curve for definite integrals
                    plot_result = plotter.plot_function(original_expr, title=f"Function: {original_expr}")
                    if plot_result['success']:
                        st.plotly_chart(plot_result['figure'], use_container_width=True)
        
        elif 'statistics' in problem_type.value:
            stats_viz = StatisticsVisualizer()
            
            if 'mean' in solution:
                # Descriptive statistics - create histogram if we have data
                data = solution_data['classification']['solving_approach']['parameters'].get('data', [])
                if data:
                    plot_result = stats_viz.plot_histogram(data, title="Data Distribution")
                    if plot_result['success']:
                        st.plotly_chart(plot_result['figure'], use_container_width=True)
            
            elif 'probability' in solution:
                # Plot probability distribution
                params = solution_data['classification']['solving_approach']['parameters']
                dist_type = params.get('prob_type', 'normal')
                
                if dist_type == 'binomial':
                    plot_params = {'n': params.get('n', 10), 'p': params.get('p', 0.5)}
                elif dist_type == 'normal':
                    plot_params = {'mean': params.get('mean', 0), 'std_dev': params.get('std_dev', 1)}
                else:
                    plot_params = {}
                
                if plot_params:
                    plot_result = stats_viz.plot_probability_distribution(dist_type, plot_params)
                    if plot_result['success']:
                        st.plotly_chart(plot_result['figure'], use_container_width=True)
    
    except Exception as e:
        st.warning(f"Could not generate visualization: {str(e)}")

def function_plotter_page():
    """Function plotting interface."""
    st.header("Interactive Function Plotter")
    
    # Function input
    function_expr = st.text_input(
        "Enter function to plot:",
        placeholder="e.g., x**2 + 2*x - 1, sin(x), exp(x), log(x)",
        value="x**2"
    )
    
    # Plot settings
    col1, col2 = st.columns(2)
    with col1:
        x_min = st.number_input("X minimum:", value=-10.0)
        x_max = st.number_input("X maximum:", value=10.0)
    
    with col2:
        variable = st.text_input("Variable:", value="x")
        plot_type = st.selectbox("Plot type:", ["Single Function", "Function + Derivatives", "Multiple Functions"])
    
    if st.button("Generate Plot"):
        plotter = FunctionPlotter()
        
        try:
            if plot_type == "Single Function":
                result = plotter.plot_function(function_expr, variable, (x_min, x_max))
            elif plot_type == "Function + Derivatives":
                result = plotter.plot_derivative_comparison(function_expr, variable, (x_min, x_max))
            else:  # Multiple Functions
                # For demo, plot function and its square
                expressions = [function_expr, f"({function_expr})**2"]
                result = plotter.plot_multiple_functions(expressions, variable, (x_min, x_max))
            
            if result['success']:
                st.plotly_chart(result['figure'], use_container_width=True)
                
                # Display function info
                if 'latex' in result:
                    st.latex(f"f({variable}) = {result['latex']}")
            else:
                st.error(f"Plotting error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            st.error(f"An error occurred: {str(e)}")

def statistics_tools_page():
    """Statistics tools interface."""
    st.header("Statistics Tools")
    
    tool_type = st.selectbox(
        "Choose tool:",
        ["Descriptive Statistics", "Probability Calculator", "Data Visualization"]
    )
    
    if tool_type == "Descriptive Statistics":
        st.subheader("Descriptive Statistics Calculator")
        
        # Data input methods
        input_method = st.radio("Data input method:", ["Manual Entry", "Upload CSV"])
        
        if input_method == "Manual Entry":
            data_input = st.text_area(
                "Enter data (comma-separated):",
                placeholder="e.g., 1, 2, 3, 4, 5, 6, 7, 8, 9, 10"
            )
            
            if data_input and st.button("Calculate Statistics"):
                try:
                    data = [float(x.strip()) for x in data_input.split(',') if x.strip()]
                    
                    solver = StatisticsSolver()
                    result = solver.descriptive_statistics(data)
                    
                    if result['success']:
                        # Display statistics
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.metric("Mean", f"{result['mean']:.3f}")
                            st.metric("Median", f"{result['median']:.3f}")
                            st.metric("Standard Deviation", f"{result['std_dev']:.3f}")
                        
                        with col2:
                            st.metric("Variance", f"{result['variance']:.3f}")
                            st.metric("Range", f"{result['range']:.3f}")
                            st.metric("Count", result['min'])
                        
                        # Visualization
                        viz = StatisticsVisualizer()
                        plot_result = viz.plot_histogram(data, title="Data Distribution")
                        if plot_result['success']:
                            st.plotly_chart(plot_result['figure'], use_container_width=True)
                    
                    else:
                        st.error(f"Calculation error: {result.get('error', 'Unknown error')}")
                        
                except ValueError:
                    st.error("Please enter valid numbers separated by commas.")
    
    elif tool_type == "Probability Calculator":
        st.subheader("Probability Calculator")
        
        dist_type = st.selectbox("Distribution type:", ["Binomial", "Normal", "Poisson"])
        
        if dist_type == "Binomial":
            col1, col2, col3 = st.columns(3)
            with col1:
                n = st.number_input("Number of trials (n):", min_value=1, value=10)
            with col2:
                k = st.number_input("Number of successes (k):", min_value=0, value=5)
            with col3:
                p = st.number_input("Probability of success (p):", min_value=0.0, max_value=1.0, value=0.5)
            
            if st.button("Calculate Probability"):
                solver = StatisticsSolver()
                result = solver.probability_calculation('binomial', n=n, k=k, p=p)
                
                if result['success']:
                    st.success(f"P(X = {k}) = {result['probability']:.6f}")
                    
                    # Show steps
                    if 'steps' in result:
                        st.subheader("Calculation Steps")
                        for step in result['steps']:
                            st.write(f"**{step['step']}:** {step['explanation']}")
                            if step.get('latex'):
                                st.latex(step['latex'])

def solution_history_page():
    """Display solution history."""
    st.header("Solution History")
    
    if not st.session_state.solution_history:
        st.info("No solutions in history yet. Solve some problems to see them here!")
        return
    
    # Display history
    for i, solution in enumerate(reversed(st.session_state.solution_history)):
        with st.expander(f"Solution {len(st.session_state.solution_history) - i}: {solution['input'][:50]}..."):
            st.write(f"**Input Method:** {solution['input_method']}")
            st.write(f"**Problem Type:** {solution['formatted']['problem_type']}")
            st.write(f"**Final Answer:** {solution['formatted']['final_result']}")
            
            if st.button(f"View Full Solution {i}", key=f"view_{i}"):
                st.session_state.current_solution = solution

def help_examples_page():
    """Help and examples page."""
    st.header("Help & Examples")
    
    tab1, tab2, tab3 = st.tabs(["Getting Started", "Examples", "Syntax Guide"])
    
    with tab1:
        st.markdown("""
        ## Welcome to Math Explainer Platform!
        
        This platform helps you solve mathematical problems with detailed explanations and visualizations.
        
        ### How to use:
        1. **Choose input method**: Mathematical expression, natural language, or multiple equations
        2. **Enter your problem**: Type your mathematical problem or describe it in plain English
        3. **Click Solve**: The platform will analyze and solve your problem
        4. **Review solution**: See step-by-step explanations and visualizations
        
        ### Features:
        - ✅ Step-by-step solutions
        - ✅ Interactive visualizations
        - ✅ Multiple input methods
        - ✅ LaTeX rendering
        - ✅ Solution history
        """)
    
    with tab2:
        st.markdown("""
        ## Example Problems
        
        ### Algebra
        - `x^2 + 2*x - 3 = 0` - Quadratic equation
        - `2*x + 3*y = 7` and `x - y = 1` - System of equations
        - `factor(x^2 - 4)` - Factoring
        
        ### Calculus
        - `d/dx(x^2 + sin(x))` - Derivative
        - `integrate(x^2, x)` - Indefinite integral
        - `integrate(x^2, (x, 0, 1))` - Definite integral
        - `limit(sin(x)/x, x, 0)` - Limit
        
        ### Statistics
        - Descriptive statistics for data: `1, 2, 3, 4, 5`
        - Binomial probability: `n=10, k=3, p=0.5`
        - Normal distribution calculations
        
        ### Natural Language
        - "Find the derivative of x squared plus sine of x"
        - "Solve x squared plus 2x equals 3"
        - "What is the integral of x squared?"
        """)
    
    with tab3:
        st.markdown("""
        ## Syntax Guide
        
        ### Mathematical Operators
        - Addition: `+`
        - Subtraction: `-`
        - Multiplication: `*`
        - Division: `/`
        - Exponentiation: `**` or `^`
        
        ### Functions
        - `sin(x)`, `cos(x)`, `tan(x)` - Trigonometric functions
        - `log(x)`, `ln(x)` - Logarithms
        - `exp(x)` - Exponential function
        - `sqrt(x)` - Square root
        - `abs(x)` - Absolute value
        
        ### Special Notation
        - `d/dx(expression)` - Derivative
        - `integrate(expression, variable)` - Integral
        - `limit(expression, variable, value)` - Limit
        - `factor(expression)` - Factoring
        - `expand(expression)` - Expansion
        """)

if __name__ == "__main__":
    main()
