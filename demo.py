"""
Math Explainer Platform - Demo Version

A simplified demonstration of the math solving capabilities without heavy dependencies.
This version works with just SymPy and basic Python libraries.
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import sympy as sp
    SYMPY_AVAILABLE = True
except ImportError:
    SYMPY_AVAILABLE = False
    print("SymPy not available. Installing...")

from math_engine import AlgebraSolver, CalculusSolver, StatisticsSolver, SolutionFormatter
from parsers import MathParser, ProblemClassifier, NaturalLanguageParser
from sample_problems import get_sample_problem, get_natural_language_problem, SAMPLE_PROBLEMS


def demo_algebra_solver():
    """Demonstrate algebra solving capabilities."""
    print("\n" + "="*60)
    print("ALGEBRA SOLVER DEMONSTRATION")
    print("="*60)
    
    solver = AlgebraSolver()
    formatter = SolutionFormatter()
    
    # Test problems
    problems = [
        "x**2 + 2*x - 3 = 0",
        "2*x + 3 = 7",
        "x**3 - 6*x**2 + 11*x - 6"  # For factoring
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\nProblem {i}: {problem}")
        print("-" * 40)
        
        if "=" in problem:
            result = solver.solve_equation(problem)
        else:
            result = solver.factor_polynomial(problem)
        
        if result['success']:
            if 'solutions' in result:
                print(f"Solutions: {result['solutions']}")
            elif 'result' in result:
                print(f"Result: {result['result']}")
            
            # Show steps
            if result.get('steps'):
                print("\nSolution Steps:")
                formatted_steps = formatter.format_steps(result['steps'])
                for step in formatted_steps:
                    print(f"  {step['step_number']}. {step['title']}")
                    if step['explanation']:
                        print(f"     {step['explanation']}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")


def demo_calculus_solver():
    """Demonstrate calculus solving capabilities."""
    print("\n" + "="*60)
    print("CALCULUS SOLVER DEMONSTRATION")
    print("="*60)
    
    solver = CalculusSolver()
    formatter = SolutionFormatter()
    
    # Test problems
    problems = [
        ("differentiate", "x**3 + 2*x**2 - x + 1"),
        ("integrate", "2*x + 3"),
        ("limit", "sin(x)/x", "0")
    ]
    
    for i, problem in enumerate(problems, 1):
        operation = problem[0]
        expression = problem[1]
        
        print(f"\nProblem {i}: {operation.title()} {expression}")
        if len(problem) > 2:
            print(f"           (limit as x approaches {problem[2]})")
        print("-" * 40)
        
        if operation == "differentiate":
            result = solver.differentiate(expression)
        elif operation == "integrate":
            result = solver.integrate(expression)
        elif operation == "limit":
            result = solver.compute_limit(expression, approach_value=problem[2])
        
        if result['success']:
            print(f"Result: {result['result']}")
            
            # Show steps
            if result.get('steps'):
                print("\nSolution Steps:")
                formatted_steps = formatter.format_steps(result['steps'])
                for step in formatted_steps:
                    print(f"  {step['step_number']}. {step['title']}")
                    if step['explanation']:
                        print(f"     {step['explanation']}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")


def demo_statistics_solver():
    """Demonstrate statistics solving capabilities."""
    print("\n" + "="*60)
    print("STATISTICS SOLVER DEMONSTRATION")
    print("="*60)
    
    solver = StatisticsSolver()
    formatter = SolutionFormatter()
    
    # Test descriptive statistics
    print("\nProblem 1: Descriptive Statistics")
    print("-" * 40)
    data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    print(f"Data: {data}")
    
    result = solver.descriptive_statistics(data)
    
    if result['success']:
        print(f"Mean: {result['mean']:.3f}")
        print(f"Median: {result['median']:.3f}")
        print(f"Standard Deviation: {result['std_dev']:.3f}")
        print(f"Variance: {result['variance']:.3f}")
        print(f"Range: {result['range']}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    # Test probability calculation
    print("\nProblem 2: Binomial Probability")
    print("-" * 40)
    print("P(X = 3) for Binomial(n=10, p=0.5)")
    
    result = solver.probability_calculation('binomial', n=10, k=3, p=0.5)
    
    if result['success']:
        print(f"Probability: {result['probability']:.6f}")
        
        # Show steps
        if result.get('steps'):
            print("\nCalculation Steps:")
            formatted_steps = formatter.format_steps(result['steps'])
            for step in formatted_steps:
                print(f"  {step['step_number']}. {step['title']}")
                if step['explanation']:
                    print(f"     {step['explanation']}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")


def demo_parsers():
    """Demonstrate parsing capabilities."""
    print("\n" + "="*60)
    print("PARSER DEMONSTRATION")
    print("="*60)
    
    math_parser = MathParser()
    classifier = ProblemClassifier()
    nl_parser = NaturalLanguageParser()
    
    # Test mathematical expression parsing
    print("\n1. Mathematical Expression Parsing")
    print("-" * 40)
    
    expressions = [
        "x**2 + 2*x - 3 = 0",
        "sin(x) + cos(x)",
        "integrate(x**2, x)"
    ]
    
    for expr in expressions:
        print(f"\nExpression: {expr}")
        
        if "=" in expr:
            result = math_parser.parse_equation(expr)
        else:
            result = math_parser.parse_expression(expr)
        
        if result['success']:
            print(f"Variables: {result.get('variables', [])}")
            print(f"Functions: {result.get('functions', [])}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
    
    # Test problem classification
    print("\n2. Problem Classification")
    print("-" * 40)
    
    problems = [
        "solve x^2 + 2*x - 3 = 0",
        "find the derivative of sin(x)",
        "calculate the mean of the data",
        "integrate x^2 dx"
    ]
    
    for problem in problems:
        print(f"\nProblem: {problem}")
        classification = classifier.classify_problem(problem)
        print(f"Type: {classification['problem_type'].value}")
        print(f"Confidence: {classification['confidence']:.2%}")
    
    # Test natural language parsing
    print("\n3. Natural Language Parsing")
    print("-" * 40)
    
    nl_problems = [
        "two plus three equals five",
        "find the derivative of x squared",
        "solve x squared minus four equals zero"
    ]
    
    for problem in nl_problems:
        print(f"\nNatural Language: {problem}")
        result = nl_parser.parse_natural_language(problem)
        
        if result['success']:
            print(f"Converted: {result['converted_text']}")
            print(f"Math Expression: {result['math_expression']}")
            print(f"Domain: {result['problem_context']['domain']}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")


def demo_complete_workflow():
    """Demonstrate complete problem-solving workflow."""
    print("\n" + "="*60)
    print("COMPLETE WORKFLOW DEMONSTRATION")
    print("="*60)
    
    # Initialize components
    math_parser = MathParser()
    classifier = ProblemClassifier()
    algebra_solver = AlgebraSolver()
    calculus_solver = CalculusSolver()
    formatter = SolutionFormatter()
    
    # Test problems
    problems = [
        "x**2 - 5*x + 6 = 0",
        "d/dx(x**3 + 2*x**2)"
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\nWorkflow {i}: {problem}")
        print("=" * 50)
        
        # Step 1: Parse the problem
        print("Step 1: Parsing...")
        if "=" in problem:
            parsed = math_parser.parse_equation(problem)
        else:
            parsed = math_parser.parse_expression(problem)
        
        if not parsed['success']:
            print(f"Parsing failed: {parsed.get('error')}")
            continue
        
        print(f"✓ Parsed successfully")
        
        # Step 2: Classify the problem
        print("\nStep 2: Classification...")
        classification = classifier.classify_problem(problem, parsed)
        print(f"✓ Problem type: {classification['problem_type'].value}")
        print(f"✓ Confidence: {classification['confidence']:.2%}")
        
        # Step 3: Solve the problem
        print("\nStep 3: Solving...")
        approach = classification['solving_approach']
        
        if approach['solver'] == 'AlgebraSolver':
            if approach['method'] == 'solve_equation':
                result = algebra_solver.solve_equation(problem)
            else:
                result = algebra_solver.factor_polynomial(problem)
        elif approach['solver'] == 'CalculusSolver':
            if approach['method'] == 'differentiate':
                expr = problem.replace('d/dx(', '').replace(')', '')
                result = calculus_solver.differentiate(expr)
            else:
                result = calculus_solver.integrate(problem)
        
        if result['success']:
            print(f"✓ Solution found")
            
            # Step 4: Format the solution
            print("\nStep 4: Formatting...")
            formatted = formatter.format_solution_summary(result)
            print(f"✓ Final answer: {formatted['final_result']}")
            
            # Show detailed steps
            print(f"\nDetailed Steps ({formatted['step_count']} steps):")
            for step in formatted['formatted_steps']:
                print(f"  {step['step_number']}. {step['title']}")
                if step['explanation']:
                    print(f"     → {step['explanation']}")
        else:
            print(f"✗ Solving failed: {result.get('error')}")


def main():
    """Main demo function."""
    print("MATH EXPLAINER PLATFORM - DEMONSTRATION")
    print("=" * 80)
    
    if not SYMPY_AVAILABLE:
        print("ERROR: SymPy is required for this demonstration.")
        print("Please install it with: pip install sympy")
        return
    
    print("This demonstration shows the core mathematical problem-solving capabilities")
    print("of the Math Explainer Platform without the web interface.")
    print("\nFeatures demonstrated:")
    print("• Algebraic equation solving with step-by-step explanations")
    print("• Calculus operations (derivatives, integrals, limits)")
    print("• Statistical calculations and probability")
    print("• Mathematical expression parsing and validation")
    print("• Problem classification and natural language processing")
    print("• Complete problem-solving workflow")
    
    try:
        # Run demonstrations
        demo_algebra_solver()
        demo_calculus_solver()
        demo_statistics_solver()
        demo_parsers()
        demo_complete_workflow()
        
        print("\n" + "="*80)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("="*80)
        print("\nTo run the full web application:")
        print("1. Install required packages: pip install -r requirements.txt")
        print("2. Run the Streamlit app: streamlit run app.py")
        print("\nFor testing:")
        print("• Run math engine tests: python tests/test_math_engine.py")
        print("• Run parser tests: python tests/test_parsers.py")
        print("• View sample problems: python sample_problems.py")
        
    except Exception as e:
        print(f"\nError during demonstration: {str(e)}")
        print("This might be due to missing dependencies.")
        print("Please ensure SymPy is installed: pip install sympy")


if __name__ == "__main__":
    main()
