"""
Algebra Solver Module

Handles algebraic equations, inequalities, systems of equations, and polynomial operations.
Provides detailed step-by-step solutions with explanations.
"""

import sympy as sp
from typing import List, Dict, Any, Tuple
from sympy import symbols, solve, expand, factor, simplify, Eq, latex


class AlgebraSolver:
    """Solver for algebraic problems with step-by-step explanations."""
    
    def __init__(self):
        self.steps = []
        self.variables = set()
    
    def solve_equation(self, equation_str: str, variable: str = None) -> Dict[str, Any]:
        """
        Solve algebraic equations with detailed steps.
        
        Args:
            equation_str: String representation of equation (e.g., "x**2 + 2*x - 3 = 0")
            variable: Variable to solve for (auto-detected if None)
            
        Returns:
            Dictionary containing solutions, steps, and explanations
        """
        self.steps = []
        
        try:
            # Parse the equation
            if '=' in equation_str:
                left, right = equation_str.split('=')
                equation = sp.Eq(sp.sympify(left.strip()), sp.sympify(right.strip()))
            else:
                equation = sp.Eq(sp.sympify(equation_str), 0)
            
            self.steps.append({
                'step': 'Parse equation',
                'expression': equation,
                'latex': sp.latex(equation),
                'explanation': f'Original equation: {equation_str}'
            })
            
            # Determine variable if not specified
            if variable is None:
                free_symbols = equation.free_symbols
                if len(free_symbols) == 1:
                    variable = list(free_symbols)[0]
                else:
                    variable = symbols('x')  # Default to x
            else:
                variable = symbols(variable)
            
            # Rearrange equation to standard form
            standard_form = sp.Eq(equation.lhs - equation.rhs, 0)
            if standard_form != equation:
                self.steps.append({
                    'step': 'Rearrange to standard form',
                    'expression': standard_form,
                    'latex': sp.latex(standard_form),
                    'explanation': 'Move all terms to one side'
                })
            
            # Simplify the expression
            simplified = simplify(standard_form.lhs)
            if simplified != standard_form.lhs:
                self.steps.append({
                    'step': 'Simplify',
                    'expression': sp.Eq(simplified, 0),
                    'latex': sp.latex(sp.Eq(simplified, 0)),
                    'explanation': 'Combine like terms and simplify'
                })
            
            # Solve the equation
            solutions = solve(equation, variable)
            
            self.steps.append({
                'step': 'Solve',
                'expression': solutions,
                'latex': [sp.latex(sol) for sol in solutions] if solutions else ['No solution'],
                'explanation': f'Solutions for {variable}: {solutions}'
            })
            
            return {
                'solutions': solutions,
                'steps': self.steps,
                'variable': str(variable),
                'original_equation': equation_str,
                'success': True
            }
            
        except Exception as e:
            return {
                'solutions': [],
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def solve_system(self, equations: List[str], variables: List[str] = None) -> Dict[str, Any]:
        """
        Solve system of equations.
        
        Args:
            equations: List of equation strings
            variables: List of variable names (auto-detected if None)
            
        Returns:
            Dictionary containing solutions and steps
        """
        self.steps = []
        
        try:
            # Parse equations
            parsed_equations = []
            for eq_str in equations:
                if '=' in eq_str:
                    left, right = eq_str.split('=')
                    eq = sp.Eq(sp.sympify(left.strip()), sp.sympify(right.strip()))
                else:
                    eq = sp.Eq(sp.sympify(eq_str), 0)
                parsed_equations.append(eq)
            
            self.steps.append({
                'step': 'Parse system',
                'expression': parsed_equations,
                'latex': [sp.latex(eq) for eq in parsed_equations],
                'explanation': f'System of {len(equations)} equations'
            })
            
            # Determine variables
            if variables is None:
                all_symbols = set()
                for eq in parsed_equations:
                    all_symbols.update(eq.free_symbols)
                variables = list(all_symbols)
            else:
                variables = [symbols(var) for var in variables]
            
            # Solve the system
            solutions = solve(parsed_equations, variables)
            
            self.steps.append({
                'step': 'Solve system',
                'expression': solutions,
                'latex': sp.latex(solutions) if solutions else 'No solution',
                'explanation': f'Solution: {solutions}'
            })
            
            return {
                'solutions': solutions,
                'steps': self.steps,
                'variables': [str(var) for var in variables],
                'success': True
            }
            
        except Exception as e:
            return {
                'solutions': {},
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def factor_polynomial(self, expression_str: str) -> Dict[str, Any]:
        """Factor polynomial expressions."""
        self.steps = []
        
        try:
            expr = sp.sympify(expression_str)
            
            self.steps.append({
                'step': 'Original expression',
                'expression': expr,
                'latex': sp.latex(expr),
                'explanation': f'Factor: {expression_str}'
            })
            
            factored = factor(expr)
            
            self.steps.append({
                'step': 'Factored form',
                'expression': factored,
                'latex': sp.latex(factored),
                'explanation': 'Factored into irreducible components'
            })
            
            return {
                'result': factored,
                'steps': self.steps,
                'success': True
            }
            
        except Exception as e:
            return {
                'result': None,
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def expand_expression(self, expression_str: str) -> Dict[str, Any]:
        """Expand algebraic expressions."""
        self.steps = []
        
        try:
            expr = sp.sympify(expression_str)
            
            self.steps.append({
                'step': 'Original expression',
                'expression': expr,
                'latex': sp.latex(expr),
                'explanation': f'Expand: {expression_str}'
            })
            
            expanded = expand(expr)
            
            self.steps.append({
                'step': 'Expanded form',
                'expression': expanded,
                'latex': sp.latex(expanded),
                'explanation': 'Distributed and combined like terms'
            })
            
            return {
                'result': expanded,
                'steps': self.steps,
                'success': True
            }
            
        except Exception as e:
            return {
                'result': None,
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
