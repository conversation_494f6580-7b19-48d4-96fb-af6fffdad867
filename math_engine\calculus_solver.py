"""
Calculus Solver Module

Handles derivatives, integrals, limits, and series expansions.
Provides detailed step-by-step solutions with explanations.
"""

import sympy as sp
from typing import List, Dict, Any, Optional
from sympy import symbols, diff, integrate, limit, series, oo, latex


class CalculusSolver:
    """Solver for calculus problems with step-by-step explanations."""
    
    def __init__(self):
        self.steps = []
    
    def differentiate(self, expression_str: str, variable: str = 'x', order: int = 1) -> Dict[str, Any]:
        """
        Compute derivatives with detailed steps.
        
        Args:
            expression_str: Function to differentiate
            variable: Variable to differentiate with respect to
            order: Order of derivative (1 for first derivative, 2 for second, etc.)
            
        Returns:
            Dictionary containing derivative, steps, and explanations
        """
        self.steps = []
        
        try:
            # Parse the expression
            expr = sp.sympify(expression_str)
            var = symbols(variable)
            
            self.steps.append({
                'step': 'Original function',
                'expression': expr,
                'latex': sp.latex(expr),
                'explanation': f'Find the {self._ordinal(order)} derivative of f({variable}) = {expression_str}'
            })
            
            # Compute derivative step by step
            current_expr = expr
            for i in range(order):
                derivative = diff(current_expr, var)
                
                # Add explanation based on the type of function
                explanation = self._get_derivative_explanation(current_expr, var, i + 1)
                
                self.steps.append({
                    'step': f'{self._ordinal(i + 1)} derivative',
                    'expression': derivative,
                    'latex': sp.latex(derivative),
                    'explanation': explanation
                })
                
                current_expr = derivative
            
            # Simplify if possible
            simplified = sp.simplify(current_expr)
            if simplified != current_expr:
                self.steps.append({
                    'step': 'Simplified',
                    'expression': simplified,
                    'latex': sp.latex(simplified),
                    'explanation': 'Simplified form'
                })
                current_expr = simplified
            
            return {
                'result': current_expr,
                'steps': self.steps,
                'variable': variable,
                'order': order,
                'success': True
            }
            
        except Exception as e:
            return {
                'result': None,
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def integrate(self, expression_str: str, variable: str = 'x', 
                 definite: bool = False, lower_limit: str = None, 
                 upper_limit: str = None) -> Dict[str, Any]:
        """
        Compute integrals with detailed steps.
        
        Args:
            expression_str: Function to integrate
            variable: Variable of integration
            definite: Whether to compute definite integral
            lower_limit: Lower limit for definite integral
            upper_limit: Upper limit for definite integral
            
        Returns:
            Dictionary containing integral, steps, and explanations
        """
        self.steps = []
        
        try:
            # Parse the expression
            expr = sp.sympify(expression_str)
            var = symbols(variable)
            
            if definite and lower_limit is not None and upper_limit is not None:
                lower = sp.sympify(lower_limit)
                upper = sp.sympify(upper_limit)
                
                self.steps.append({
                    'step': 'Definite integral setup',
                    'expression': expr,
                    'latex': f'\\int_{{{sp.latex(lower)}}}^{{{sp.latex(upper)}}} {sp.latex(expr)} \\, d{variable}',
                    'explanation': f'Compute definite integral from {lower_limit} to {upper_limit}'
                })
                
                # First find antiderivative
                antiderivative = integrate(expr, var)
                self.steps.append({
                    'step': 'Find antiderivative',
                    'expression': antiderivative,
                    'latex': sp.latex(antiderivative),
                    'explanation': 'Find the antiderivative (indefinite integral)'
                })
                
                # Evaluate definite integral
                result = integrate(expr, (var, lower, upper))
                self.steps.append({
                    'step': 'Evaluate definite integral',
                    'expression': result,
                    'latex': sp.latex(result),
                    'explanation': f'Apply fundamental theorem: F({upper_limit}) - F({lower_limit})'
                })
                
            else:
                self.steps.append({
                    'step': 'Indefinite integral',
                    'expression': expr,
                    'latex': f'\\int {sp.latex(expr)} \\, d{variable}',
                    'explanation': f'Find the antiderivative of {expression_str}'
                })
                
                result = integrate(expr, var)
                
                explanation = self._get_integration_explanation(expr, var)
                self.steps.append({
                    'step': 'Result',
                    'expression': result,
                    'latex': sp.latex(result) + ' + C',
                    'explanation': explanation + ' (plus constant of integration)'
                })
            
            return {
                'result': result,
                'steps': self.steps,
                'variable': variable,
                'definite': definite,
                'success': True
            }
            
        except Exception as e:
            return {
                'result': None,
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def compute_limit(self, expression_str: str, variable: str = 'x', 
                     approach_value: str = '0', direction: str = 'both') -> Dict[str, Any]:
        """
        Compute limits with detailed steps.
        
        Args:
            expression_str: Function expression
            variable: Variable approaching the limit
            approach_value: Value that variable approaches
            direction: 'left', 'right', or 'both'
            
        Returns:
            Dictionary containing limit, steps, and explanations
        """
        self.steps = []
        
        try:
            expr = sp.sympify(expression_str)
            var = symbols(variable)
            approach = sp.sympify(approach_value)
            
            self.steps.append({
                'step': 'Limit setup',
                'expression': expr,
                'latex': f'\\lim_{{{variable} \\to {sp.latex(approach)}}} {sp.latex(expr)}',
                'explanation': f'Find limit as {variable} approaches {approach_value}'
            })
            
            # Check for indeterminate forms
            try:
                direct_sub = expr.subs(var, approach)
                self.steps.append({
                    'step': 'Direct substitution',
                    'expression': direct_sub,
                    'latex': sp.latex(direct_sub),
                    'explanation': f'Substitute {variable} = {approach_value}'
                })
            except:
                pass
            
            # Compute the limit
            if direction == 'left':
                result = limit(expr, var, approach, '-')
            elif direction == 'right':
                result = limit(expr, var, approach, '+')
            else:
                result = limit(expr, var, approach)
            
            self.steps.append({
                'step': 'Limit result',
                'expression': result,
                'latex': sp.latex(result),
                'explanation': f'The limit is {result}'
            })
            
            return {
                'result': result,
                'steps': self.steps,
                'variable': variable,
                'approach_value': approach_value,
                'success': True
            }
            
        except Exception as e:
            return {
                'result': None,
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def _ordinal(self, n: int) -> str:
        """Convert number to ordinal string."""
        if n == 1:
            return "first"
        elif n == 2:
            return "second"
        elif n == 3:
            return "third"
        else:
            return f"{n}th"
    
    def _get_derivative_explanation(self, expr, var, order: int) -> str:
        """Generate explanation for derivative step."""
        if order == 1:
            if expr.is_polynomial(var):
                return "Apply power rule: d/dx[x^n] = n*x^(n-1)"
            elif expr.has(sp.sin, sp.cos):
                return "Apply trigonometric derivatives"
            elif expr.has(sp.exp):
                return "Apply exponential rule: d/dx[e^x] = e^x"
            elif expr.has(sp.log):
                return "Apply logarithmic rule: d/dx[ln(x)] = 1/x"
            else:
                return "Apply differentiation rules"
        else:
            return f"Apply differentiation rules for {self._ordinal(order)} derivative"
    
    def _get_integration_explanation(self, expr, var) -> str:
        """Generate explanation for integration step."""
        if expr.is_polynomial(var):
            return "Apply power rule for integration: ∫x^n dx = x^(n+1)/(n+1)"
        elif expr.has(sp.sin, sp.cos):
            return "Apply trigonometric integration formulas"
        elif expr.has(sp.exp):
            return "Apply exponential integration: ∫e^x dx = e^x"
        elif expr.has(1/var):
            return "Apply logarithmic integration: ∫1/x dx = ln|x|"
        else:
            return "Apply integration techniques"
