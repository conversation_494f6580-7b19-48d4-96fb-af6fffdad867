"""
Solution Formatter Module

Formats mathematical solutions for display with LaTeX rendering and explanations.
"""

import sympy as sp
from typing import List, Dict, Any
import re


class SolutionFormatter:
    """Formats mathematical solutions for display."""
    
    def __init__(self):
        pass
    
    def format_steps(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format solution steps for display.
        
        Args:
            steps: List of solution steps
            
        Returns:
            Formatted steps with enhanced display information
        """
        formatted_steps = []
        
        for i, step in enumerate(steps):
            formatted_step = {
                'step_number': i + 1,
                'title': step.get('step', f'Step {i + 1}'),
                'expression': step.get('expression'),
                'latex': step.get('latex', ''),
                'explanation': step.get('explanation', ''),
                'formatted_latex': self._enhance_latex(step.get('latex', '')),
                'step_type': self._classify_step(step.get('step', ''))
            }
            
            formatted_steps.append(formatted_step)
        
        return formatted_steps
    
    def _enhance_latex(self, latex_str: str) -> str:
        """Enhance LaTeX formatting for better display."""
        if not latex_str:
            return ''
        
        # Ensure proper LaTeX delimiters
        if not latex_str.startswith('$') and not latex_str.startswith('\\['):
            latex_str = f'$${latex_str}$$'
        
        # Clean up common formatting issues
        latex_str = latex_str.replace('**', '^')
        latex_str = latex_str.replace('*', ' \\cdot ')
        
        return latex_str
    
    def _classify_step(self, step_title: str) -> str:
        """Classify the type of step for styling purposes."""
        step_title_lower = step_title.lower()
        
        if any(word in step_title_lower for word in ['parse', 'original', 'setup']):
            return 'setup'
        elif any(word in step_title_lower for word in ['solve', 'result', 'solution']):
            return 'solution'
        elif any(word in step_title_lower for word in ['simplify', 'factor', 'expand']):
            return 'transformation'
        elif any(word in step_title_lower for word in ['derivative', 'integral', 'limit']):
            return 'calculus'
        elif any(word in step_title_lower for word in ['probability', 'statistics', 'mean']):
            return 'statistics'
        else:
            return 'general'
    
    def format_solution_summary(self, solution_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a formatted summary of the solution.
        
        Args:
            solution_data: Complete solution data
            
        Returns:
            Formatted summary for display
        """
        summary = {
            'success': solution_data.get('success', False),
            'problem_type': self._identify_problem_type(solution_data),
            'final_result': self._format_final_result(solution_data),
            'step_count': len(solution_data.get('steps', [])),
            'formatted_steps': self.format_steps(solution_data.get('steps', []))
        }
        
        if not summary['success']:
            summary['error_message'] = solution_data.get('error', 'Unknown error occurred')
        
        return summary
    
    def _identify_problem_type(self, solution_data: Dict[str, Any]) -> str:
        """Identify the type of mathematical problem."""
        if 'solutions' in solution_data:
            return 'Algebraic Equation'
        elif 'result' in solution_data and 'variable' in solution_data:
            if 'order' in solution_data:
                return 'Calculus - Derivative'
            elif 'definite' in solution_data:
                return 'Calculus - Integral'
            else:
                return 'Calculus'
        elif 'probability' in solution_data:
            return 'Statistics - Probability'
        elif 'mean' in solution_data:
            return 'Statistics - Descriptive'
        else:
            return 'Mathematical Problem'
    
    def _format_final_result(self, solution_data: Dict[str, Any]) -> str:
        """Format the final result for display."""
        if not solution_data.get('success', False):
            return 'No solution found'
        
        if 'solutions' in solution_data:
            solutions = solution_data['solutions']
            if isinstance(solutions, list):
                if len(solutions) == 0:
                    return 'No solution'
                elif len(solutions) == 1:
                    return f'x = {solutions[0]}'
                else:
                    return f'x = {", ".join(map(str, solutions))}'
            else:
                return str(solutions)
        
        elif 'result' in solution_data:
            result = solution_data['result']
            return str(result)
        
        elif 'probability' in solution_data:
            prob = solution_data['probability']
            return f'P = {prob:.6f}'
        
        elif 'mean' in solution_data:
            mean = solution_data['mean']
            return f'Mean = {mean:.4f}'
        
        else:
            return 'Solution completed'
    
    def generate_explanation_text(self, solution_data: Dict[str, Any]) -> str:
        """
        Generate a comprehensive explanation of the solution process.
        
        Args:
            solution_data: Complete solution data
            
        Returns:
            Formatted explanation text
        """
        if not solution_data.get('success', False):
            return f"Error: {solution_data.get('error', 'Unknown error occurred')}"
        
        explanation_parts = []
        
        # Problem identification
        problem_type = self._identify_problem_type(solution_data)
        explanation_parts.append(f"**Problem Type:** {problem_type}")
        
        # Solution overview
        steps = solution_data.get('steps', [])
        if steps:
            explanation_parts.append(f"**Solution Process:** {len(steps)} steps")
            
            # Key steps summary
            key_steps = [step for step in steps if step.get('step', '').lower() in 
                        ['solve', 'result', 'solution', 'final answer']]
            
            if key_steps:
                explanation_parts.append("**Key Steps:**")
                for step in key_steps:
                    explanation_parts.append(f"- {step.get('explanation', step.get('step', ''))}")
        
        # Final result
        final_result = self._format_final_result(solution_data)
        explanation_parts.append(f"**Final Answer:** {final_result}")
        
        return "\n\n".join(explanation_parts)
    
    def export_solution(self, solution_data: Dict[str, Any], format_type: str = 'markdown') -> str:
        """
        Export solution in various formats.
        
        Args:
            solution_data: Complete solution data
            format_type: Export format ('markdown', 'latex', 'plain')
            
        Returns:
            Formatted solution string
        """
        if format_type == 'markdown':
            return self._export_markdown(solution_data)
        elif format_type == 'latex':
            return self._export_latex(solution_data)
        elif format_type == 'plain':
            return self._export_plain_text(solution_data)
        else:
            raise ValueError(f"Unsupported format type: {format_type}")
    
    def _export_markdown(self, solution_data: Dict[str, Any]) -> str:
        """Export solution as Markdown."""
        lines = []
        
        # Title
        problem_type = self._identify_problem_type(solution_data)
        lines.append(f"# {problem_type} Solution")
        lines.append("")
        
        # Steps
        steps = solution_data.get('steps', [])
        for i, step in enumerate(steps, 1):
            lines.append(f"## Step {i}: {step.get('step', '')}")
            
            if step.get('latex'):
                lines.append(f"$$${step['latex']}$$$")
            
            if step.get('explanation'):
                lines.append(step['explanation'])
            
            lines.append("")
        
        # Final result
        final_result = self._format_final_result(solution_data)
        lines.append(f"## Final Answer")
        lines.append(final_result)
        
        return "\n".join(lines)
    
    def _export_latex(self, solution_data: Dict[str, Any]) -> str:
        """Export solution as LaTeX document."""
        lines = []
        
        lines.append("\\documentclass{article}")
        lines.append("\\usepackage{amsmath}")
        lines.append("\\usepackage{amsfonts}")
        lines.append("\\begin{document}")
        
        # Title
        problem_type = self._identify_problem_type(solution_data)
        lines.append(f"\\title{{{problem_type} Solution}}")
        lines.append("\\maketitle")
        
        # Steps
        steps = solution_data.get('steps', [])
        for i, step in enumerate(steps, 1):
            lines.append(f"\\section{{Step {i}: {step.get('step', '')}}}")
            
            if step.get('latex'):
                lines.append(f"\\[{step['latex']}\\]")
            
            if step.get('explanation'):
                lines.append(step['explanation'])
        
        lines.append("\\end{document}")
        
        return "\n".join(lines)
    
    def _export_plain_text(self, solution_data: Dict[str, Any]) -> str:
        """Export solution as plain text."""
        lines = []
        
        # Title
        problem_type = self._identify_problem_type(solution_data)
        lines.append(f"{problem_type} Solution")
        lines.append("=" * len(f"{problem_type} Solution"))
        lines.append("")
        
        # Steps
        steps = solution_data.get('steps', [])
        for i, step in enumerate(steps, 1):
            lines.append(f"Step {i}: {step.get('step', '')}")
            
            if step.get('expression'):
                lines.append(f"Expression: {step['expression']}")
            
            if step.get('explanation'):
                lines.append(f"Explanation: {step['explanation']}")
            
            lines.append("")
        
        # Final result
        final_result = self._format_final_result(solution_data)
        lines.append(f"Final Answer: {final_result}")
        
        return "\n".join(lines)
