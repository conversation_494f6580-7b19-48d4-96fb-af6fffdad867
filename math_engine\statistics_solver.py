"""
Statistics Solver Module

Handles statistical calculations, probability distributions, and data analysis.
Provides detailed step-by-step solutions with explanations.
"""

import sympy as sp
import numpy as np
from typing import List, Dict, Any, Optional
from sympy import symbols, summation, factorial, binomial, latex
from sympy.stats import *


class StatisticsSolver:
    """Solver for statistics problems with step-by-step explanations."""
    
    def __init__(self):
        self.steps = []
    
    def descriptive_statistics(self, data: List[float]) -> Dict[str, Any]:
        """
        Calculate descriptive statistics for a dataset.
        
        Args:
            data: List of numerical values
            
        Returns:
            Dictionary containing statistics and steps
        """
        self.steps = []
        
        try:
            data_array = np.array(data)
            n = len(data)
            
            self.steps.append({
                'step': 'Dataset',
                'expression': data,
                'latex': f'n = {n}, \\text{{data}} = [{", ".join(map(str, data))}]',
                'explanation': f'Dataset with {n} observations'
            })
            
            # Mean
            mean = np.mean(data_array)
            self.steps.append({
                'step': 'Mean',
                'expression': mean,
                'latex': f'\\bar{{x}} = \\frac{{\\sum x_i}}{{n}} = \\frac{{{sum(data)}}}{{{n}}} = {mean:.4f}',
                'explanation': 'Sum of all values divided by number of observations'
            })
            
            # Median
            median = np.median(data_array)
            sorted_data = sorted(data)
            self.steps.append({
                'step': 'Median',
                'expression': median,
                'latex': f'\\text{{Median}} = {median}',
                'explanation': f'Middle value of sorted data: {sorted_data}'
            })
            
            # Standard deviation
            std_dev = np.std(data_array, ddof=1)  # Sample standard deviation
            variance = np.var(data_array, ddof=1)
            
            self.steps.append({
                'step': 'Variance',
                'expression': variance,
                'latex': f's^2 = \\frac{{\\sum (x_i - \\bar{{x}})^2}}{{n-1}} = {variance:.4f}',
                'explanation': 'Sample variance (sum of squared deviations divided by n-1)'
            })
            
            self.steps.append({
                'step': 'Standard Deviation',
                'expression': std_dev,
                'latex': f's = \\sqrt{{s^2}} = \\sqrt{{{variance:.4f}}} = {std_dev:.4f}',
                'explanation': 'Square root of variance'
            })
            
            # Range
            data_range = max(data) - min(data)
            self.steps.append({
                'step': 'Range',
                'expression': data_range,
                'latex': f'\\text{{Range}} = \\max - \\min = {max(data)} - {min(data)} = {data_range}',
                'explanation': 'Difference between maximum and minimum values'
            })
            
            return {
                'mean': mean,
                'median': median,
                'variance': variance,
                'std_dev': std_dev,
                'range': data_range,
                'min': min(data),
                'max': max(data),
                'steps': self.steps,
                'success': True
            }
            
        except Exception as e:
            return {
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def probability_calculation(self, prob_type: str, **kwargs) -> Dict[str, Any]:
        """
        Calculate various probability problems.
        
        Args:
            prob_type: Type of probability ('binomial', 'normal', 'poisson', etc.)
            **kwargs: Parameters specific to the probability type
            
        Returns:
            Dictionary containing probability and steps
        """
        self.steps = []
        
        try:
            if prob_type.lower() == 'binomial':
                return self._binomial_probability(**kwargs)
            elif prob_type.lower() == 'normal':
                return self._normal_probability(**kwargs)
            elif prob_type.lower() == 'poisson':
                return self._poisson_probability(**kwargs)
            else:
                return {
                    'error': f'Probability type {prob_type} not supported',
                    'success': False
                }
                
        except Exception as e:
            return {
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def _binomial_probability(self, n: int, k: int, p: float) -> Dict[str, Any]:
        """Calculate binomial probability P(X = k)."""
        
        self.steps.append({
            'step': 'Binomial setup',
            'expression': f'X ~ Binomial(n={n}, p={p})',
            'latex': f'X \\sim \\text{{Binomial}}(n={n}, p={p})',
            'explanation': f'Binomial distribution with {n} trials and success probability {p}'
        })
        
        # Calculate binomial coefficient
        binom_coeff = int(binomial(n, k))
        self.steps.append({
            'step': 'Binomial coefficient',
            'expression': binom_coeff,
            'latex': f'\\binom{{{n}}}{{{k}}} = \\frac{{{n}!}}{{{k}!({n}-{k})!}} = {binom_coeff}',
            'explanation': f'Number of ways to choose {k} successes from {n} trials'
        })
        
        # Calculate probability
        prob = float(binom_coeff * (p ** k) * ((1 - p) ** (n - k)))
        
        self.steps.append({
            'step': 'Binomial probability',
            'expression': prob,
            'latex': f'P(X = {k}) = \\binom{{{n}}}{{{k}}} \\cdot {p}^{{{k}}} \\cdot {1-p}^{{{n-k}}} = {prob:.6f}',
            'explanation': f'Probability of exactly {k} successes'
        })
        
        return {
            'probability': prob,
            'steps': self.steps,
            'distribution': 'binomial',
            'parameters': {'n': n, 'k': k, 'p': p},
            'success': True
        }
    
    def _normal_probability(self, mean: float, std_dev: float, x: float, 
                          prob_type: str = 'less_than') -> Dict[str, Any]:
        """Calculate normal distribution probabilities."""
        
        self.steps.append({
            'step': 'Normal distribution setup',
            'expression': f'X ~ N({mean}, {std_dev}²)',
            'latex': f'X \\sim N({mean}, {std_dev}^2)',
            'explanation': f'Normal distribution with mean {mean} and standard deviation {std_dev}'
        })
        
        # Standardize
        z_score = (x - mean) / std_dev
        self.steps.append({
            'step': 'Standardization',
            'expression': z_score,
            'latex': f'Z = \\frac{{X - \\mu}}{{\\sigma}} = \\frac{{{x} - {mean}}}{{{std_dev}}} = {z_score:.4f}',
            'explanation': 'Convert to standard normal distribution'
        })
        
        # Calculate probability (approximation)
        if prob_type == 'less_than':
            # Using error function approximation
            prob = 0.5 * (1 + sp.erf(z_score / sp.sqrt(2)).evalf())
            latex_expr = f'P(X < {x}) = P(Z < {z_score:.4f}) \\approx {float(prob):.6f}'
        else:
            prob = 0.5 * (1 - sp.erf(z_score / sp.sqrt(2)).evalf())
            latex_expr = f'P(X > {x}) = P(Z > {z_score:.4f}) \\approx {float(prob):.6f}'
        
        self.steps.append({
            'step': 'Normal probability',
            'expression': float(prob),
            'latex': latex_expr,
            'explanation': f'Probability using standard normal table'
        })
        
        return {
            'probability': float(prob),
            'z_score': z_score,
            'steps': self.steps,
            'distribution': 'normal',
            'parameters': {'mean': mean, 'std_dev': std_dev, 'x': x},
            'success': True
        }
    
    def _poisson_probability(self, lambda_param: float, k: int) -> Dict[str, Any]:
        """Calculate Poisson probability P(X = k)."""
        
        self.steps.append({
            'step': 'Poisson setup',
            'expression': f'X ~ Poisson(λ={lambda_param})',
            'latex': f'X \\sim \\text{{Poisson}}(\\lambda={lambda_param})',
            'explanation': f'Poisson distribution with rate parameter λ = {lambda_param}'
        })
        
        # Calculate probability
        prob = float((lambda_param ** k) * sp.exp(-lambda_param) / factorial(k))
        
        self.steps.append({
            'step': 'Poisson probability',
            'expression': prob,
            'latex': f'P(X = {k}) = \\frac{{\\lambda^k e^{{-\\lambda}}}}{{k!}} = \\frac{{{lambda_param}^{k} e^{{-{lambda_param}}}}}{{{k}!}} = {prob:.6f}',
            'explanation': f'Probability of exactly {k} events'
        })
        
        return {
            'probability': prob,
            'steps': self.steps,
            'distribution': 'poisson',
            'parameters': {'lambda': lambda_param, 'k': k},
            'success': True
        }
    
    def hypothesis_test(self, test_type: str, **kwargs) -> Dict[str, Any]:
        """
        Perform hypothesis tests.
        
        Args:
            test_type: Type of test ('one_sample_t', 'two_sample_t', etc.)
            **kwargs: Test parameters
            
        Returns:
            Dictionary containing test results and steps
        """
        self.steps = []
        
        try:
            if test_type == 'one_sample_t':
                return self._one_sample_t_test(**kwargs)
            else:
                return {
                    'error': f'Test type {test_type} not implemented',
                    'success': False
                }
                
        except Exception as e:
            return {
                'steps': self.steps,
                'error': str(e),
                'success': False
            }
    
    def _one_sample_t_test(self, sample_mean: float, population_mean: float, 
                          sample_std: float, n: int, alpha: float = 0.05) -> Dict[str, Any]:
        """Perform one-sample t-test."""
        
        self.steps.append({
            'step': 'Hypotheses',
            'expression': f'H₀: μ = {population_mean}, H₁: μ ≠ {population_mean}',
            'latex': f'H_0: \\mu = {population_mean}, \\quad H_1: \\mu \\neq {population_mean}',
            'explanation': 'Null and alternative hypotheses'
        })
        
        # Calculate t-statistic
        t_stat = (sample_mean - population_mean) / (sample_std / np.sqrt(n))
        
        self.steps.append({
            'step': 'Test statistic',
            'expression': t_stat,
            'latex': f't = \\frac{{\\bar{{x}} - \\mu_0}}{{s/\\sqrt{{n}}}} = \\frac{{{sample_mean} - {population_mean}}}{{{sample_std}/\\sqrt{{{n}}}}} = {t_stat:.4f}',
            'explanation': 'Calculate t-statistic'
        })
        
        # Degrees of freedom
        df = n - 1
        self.steps.append({
            'step': 'Degrees of freedom',
            'expression': df,
            'latex': f'df = n - 1 = {n} - 1 = {df}',
            'explanation': 'Degrees of freedom for t-distribution'
        })
        
        return {
            't_statistic': t_stat,
            'degrees_of_freedom': df,
            'alpha': alpha,
            'steps': self.steps,
            'success': True
        }
