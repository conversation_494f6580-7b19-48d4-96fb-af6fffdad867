"""
Math Parser Module

Parses mathematical expressions and converts them to SymPy format.
Handles various mathematical notations and formats.
"""

import re
import sympy as sp
from typing import Dict, Any, List, Tuple, Optional


class MathParser:
    """Parser for mathematical expressions and equations."""
    
    def __init__(self):
        self.function_mappings = {
            'sin': 'sin',
            'cos': 'cos',
            'tan': 'tan',
            'ln': 'log',
            'log': 'log',
            'exp': 'exp',
            'sqrt': 'sqrt',
            'abs': 'Abs',
            'factorial': 'factorial'
        }
        
        self.operator_mappings = {
            '^': '**',
            '÷': '/',
            '×': '*',
            '·': '*'
        }
    
    def parse_expression(self, expression: str) -> Dict[str, Any]:
        """
        Parse a mathematical expression string.
        
        Args:
            expression: Mathematical expression as string
            
        Returns:
            Dictionary containing parsed expression and metadata
        """
        try:
            # Clean and normalize the expression
            cleaned_expr = self._clean_expression(expression)
            
            # Convert to SymPy format
            sympy_expr = self._convert_to_sympy(cleaned_expr)
            
            # Parse the expression
            parsed_expr = sp.sympify(sympy_expr)
            
            # Extract metadata
            variables = list(parsed_expr.free_symbols)
            functions = self._extract_functions(parsed_expr)
            
            return {
                'original': expression,
                'cleaned': cleaned_expr,
                'sympy_format': sympy_expr,
                'parsed': parsed_expr,
                'variables': [str(var) for var in variables],
                'functions': functions,
                'latex': sp.latex(parsed_expr),
                'success': True
            }
            
        except Exception as e:
            return {
                'original': expression,
                'error': str(e),
                'success': False
            }
    
    def parse_equation(self, equation: str) -> Dict[str, Any]:
        """
        Parse an equation (expression with equals sign).
        
        Args:
            equation: Equation string (e.g., "x^2 + 2*x = 3")
            
        Returns:
            Dictionary containing parsed equation components
        """
        try:
            # Check if it's an equation
            if '=' not in equation:
                return {
                    'error': 'No equals sign found in equation',
                    'success': False
                }
            
            # Split into left and right sides
            parts = equation.split('=')
            if len(parts) != 2:
                return {
                    'error': 'Equation must have exactly one equals sign',
                    'success': False
                }
            
            left_str, right_str = parts[0].strip(), parts[1].strip()
            
            # Parse both sides
            left_result = self.parse_expression(left_str)
            right_result = self.parse_expression(right_str)
            
            if not left_result['success'] or not right_result['success']:
                return {
                    'error': 'Failed to parse equation sides',
                    'left_error': left_result.get('error'),
                    'right_error': right_result.get('error'),
                    'success': False
                }
            
            # Create SymPy equation
            equation_obj = sp.Eq(left_result['parsed'], right_result['parsed'])
            
            # Combine variables from both sides
            all_variables = list(set(left_result['variables'] + right_result['variables']))
            
            return {
                'original': equation,
                'left_side': left_result,
                'right_side': right_result,
                'equation': equation_obj,
                'variables': all_variables,
                'latex': sp.latex(equation_obj),
                'success': True
            }
            
        except Exception as e:
            return {
                'original': equation,
                'error': str(e),
                'success': False
            }
    
    def parse_system(self, equations: List[str]) -> Dict[str, Any]:
        """
        Parse a system of equations.
        
        Args:
            equations: List of equation strings
            
        Returns:
            Dictionary containing parsed system
        """
        try:
            parsed_equations = []
            all_variables = set()
            
            for i, eq_str in enumerate(equations):
                eq_result = self.parse_equation(eq_str)
                
                if not eq_result['success']:
                    return {
                        'error': f'Failed to parse equation {i+1}: {eq_result.get("error")}',
                        'success': False
                    }
                
                parsed_equations.append(eq_result)
                all_variables.update(eq_result['variables'])
            
            return {
                'original_equations': equations,
                'parsed_equations': parsed_equations,
                'variables': list(all_variables),
                'equation_objects': [eq['equation'] for eq in parsed_equations],
                'success': True
            }
            
        except Exception as e:
            return {
                'original_equations': equations,
                'error': str(e),
                'success': False
            }
    
    def _clean_expression(self, expression: str) -> str:
        """Clean and normalize mathematical expression."""
        # Remove extra whitespace
        expr = re.sub(r'\s+', ' ', expression.strip())
        
        # Replace common mathematical symbols
        for old, new in self.operator_mappings.items():
            expr = expr.replace(old, new)
        
        # Handle implicit multiplication
        expr = self._add_implicit_multiplication(expr)
        
        # Handle function names
        expr = self._normalize_functions(expr)
        
        # Handle parentheses and brackets
        expr = expr.replace('[', '(').replace(']', ')')
        
        return expr
    
    def _add_implicit_multiplication(self, expression: str) -> str:
        """Add explicit multiplication signs where implicit."""
        # Pattern for number followed by variable/function
        expr = re.sub(r'(\d)([a-zA-Z])', r'\1*\2', expression)
        
        # Pattern for closing parenthesis followed by variable/function
        expr = re.sub(r'\)([a-zA-Z])', r')*\1', expr)
        
        # Pattern for variable followed by opening parenthesis
        expr = re.sub(r'([a-zA-Z])\(', r'\1*(', expr)
        
        return expr
    
    def _normalize_functions(self, expression: str) -> str:
        """Normalize function names to SymPy format."""
        for func_name, sympy_name in self.function_mappings.items():
            # Use word boundaries to avoid partial replacements
            pattern = r'\b' + re.escape(func_name) + r'\b'
            expression = re.sub(pattern, sympy_name, expression)
        
        return expression
    
    def _convert_to_sympy(self, expression: str) -> str:
        """Convert expression to SymPy-compatible format."""
        # Handle special cases
        expr = expression
        
        # Convert power notation
        expr = expr.replace('^', '**')
        
        # Handle square root notation
        expr = re.sub(r'√\(([^)]+)\)', r'sqrt(\1)', expr)
        expr = re.sub(r'√(\w+)', r'sqrt(\1)', expr)
        
        # Handle absolute value notation
        expr = re.sub(r'\|([^|]+)\|', r'Abs(\1)', expr)
        
        # Handle factorial notation
        expr = re.sub(r'(\w+)!', r'factorial(\1)', expr)
        
        return expr
    
    def _extract_functions(self, expr) -> List[str]:
        """Extract function names from SymPy expression."""
        functions = []
        
        for atom in sp.preorder_traversal(expr):
            if isinstance(atom, sp.Function):
                func_name = atom.func.__name__
                if func_name not in functions:
                    functions.append(func_name)
        
        return functions
    
    def validate_expression(self, expression: str) -> Dict[str, Any]:
        """
        Validate if an expression can be parsed correctly.
        
        Args:
            expression: Expression to validate
            
        Returns:
            Validation result with suggestions if invalid
        """
        result = self.parse_expression(expression)
        
        if result['success']:
            return {
                'valid': True,
                'message': 'Expression is valid',
                'parsed_expression': result['parsed'],
                'latex': result['latex']
            }
        else:
            suggestions = self._generate_suggestions(expression, result['error'])
            return {
                'valid': False,
                'error': result['error'],
                'suggestions': suggestions
            }
    
    def _generate_suggestions(self, expression: str, error: str) -> List[str]:
        """Generate suggestions for fixing invalid expressions."""
        suggestions = []
        
        # Common error patterns and suggestions
        if 'invalid syntax' in error.lower():
            suggestions.append("Check for missing operators or parentheses")
            suggestions.append("Ensure all parentheses are properly matched")
        
        if 'undefined' in error.lower():
            suggestions.append("Check variable and function names")
            suggestions.append("Use standard mathematical notation")
        
        # Check for common notation issues
        if '^' in expression:
            suggestions.append("Use ** for exponentiation instead of ^")
        
        if re.search(r'\d[a-zA-Z]', expression):
            suggestions.append("Add explicit multiplication: 2x should be 2*x")
        
        if '√' in expression:
            suggestions.append("Use sqrt() function for square roots")
        
        return suggestions
