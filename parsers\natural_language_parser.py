"""
Natural Language Parser Module

Converts natural language mathematical problems into structured mathematical expressions.
"""

import re
from typing import Dict, Any, List, Tuple, Optional


class NaturalLanguageParser:
    """Parser for natural language mathematical problems."""
    
    def __init__(self):
        self.word_to_number = {
            'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
            'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9',
            'ten': '10', 'eleven': '11', 'twelve': '12', 'thirteen': '13',
            'fourteen': '14', 'fifteen': '15', 'sixteen': '16', 'seventeen': '17',
            'eighteen': '18', 'nineteen': '19', 'twenty': '20', 'thirty': '30',
            'forty': '40', 'fifty': '50', 'sixty': '60', 'seventy': '70',
            'eighty': '80', 'ninety': '90', 'hundred': '100', 'thousand': '1000'
        }
        
        self.operation_phrases = {
            'plus': '+',
            'add': '+',
            'added to': '+',
            'sum of': '+',
            'increased by': '+',
            'more than': '+',
            'minus': '-',
            'subtract': '-',
            'subtracted from': '-',
            'difference': '-',
            'decreased by': '-',
            'less than': '-',
            'times': '*',
            'multiply': '*',
            'multiplied by': '*',
            'product of': '*',
            'of': '*',
            'divide': '/',
            'divided by': '/',
            'quotient': '/',
            'over': '/',
            'squared': '**2',
            'cubed': '**3',
            'to the power of': '**',
            'raised to': '**',
            'square root of': 'sqrt(',
            'sqrt': 'sqrt('
        }
        
        self.variable_phrases = {
            'a number': 'x',
            'some number': 'x',
            'the number': 'x',
            'unknown': 'x',
            'variable': 'x'
        }
        
        self.equation_phrases = {
            'equals': '=',
            'is equal to': '=',
            'is': '=',
            'gives': '=',
            'results in': '=',
            'makes': '='
        }
    
    def parse_natural_language(self, text: str) -> Dict[str, Any]:
        """
        Parse natural language mathematical problem.
        
        Args:
            text: Natural language description of mathematical problem
            
        Returns:
            Dictionary containing parsed mathematical expression
        """
        try:
            # Clean and normalize the text
            cleaned_text = self._clean_text(text)
            
            # Convert words to numbers
            number_converted = self._convert_words_to_numbers(cleaned_text)
            
            # Convert operation phrases
            operation_converted = self._convert_operations(number_converted)
            
            # Convert variable phrases
            variable_converted = self._convert_variables(operation_converted)
            
            # Convert equation phrases
            equation_converted = self._convert_equations(variable_converted)
            
            # Extract mathematical expression
            math_expression = self._extract_math_expression(equation_converted)
            
            # Identify problem type from context
            problem_context = self._identify_context(text)
            
            return {
                'original_text': text,
                'cleaned_text': cleaned_text,
                'converted_text': equation_converted,
                'math_expression': math_expression,
                'problem_context': problem_context,
                'success': True
            }
            
        except Exception as e:
            return {
                'original_text': text,
                'error': str(e),
                'success': False
            }
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize input text."""
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove punctuation except mathematical symbols
        text = re.sub(r'[^\w\s+\-*/=().,]', '', text)
        
        return text
    
    def _convert_words_to_numbers(self, text: str) -> str:
        """Convert written numbers to digits."""
        result = text
        
        # Simple word-to-number conversion
        for word, number in self.word_to_number.items():
            pattern = r'\b' + re.escape(word) + r'\b'
            result = re.sub(pattern, number, result)
        
        # Handle compound numbers like "twenty-five"
        result = self._handle_compound_numbers(result)
        
        return result
    
    def _handle_compound_numbers(self, text: str) -> str:
        """Handle compound numbers like twenty-five, thirty-two, etc."""
        # Pattern for tens-ones combinations
        tens_ones_pattern = r'\b(20|30|40|50|60|70|80|90)\s+(1|2|3|4|5|6|7|8|9)\b'
        
        def replace_compound(match):
            tens = int(match.group(1))
            ones = int(match.group(2))
            return str(tens + ones)
        
        return re.sub(tens_ones_pattern, replace_compound, text)
    
    def _convert_operations(self, text: str) -> str:
        """Convert operation phrases to mathematical symbols."""
        result = text
        
        # Sort by length (longest first) to avoid partial replacements
        sorted_phrases = sorted(self.operation_phrases.items(), key=lambda x: len(x[0]), reverse=True)
        
        for phrase, symbol in sorted_phrases:
            pattern = r'\b' + re.escape(phrase) + r'\b'
            if symbol.endswith('('):
                # For functions like sqrt, we need to handle the closing parenthesis
                result = re.sub(pattern, symbol, result)
                # Add closing parenthesis after the next number/variable
                result = re.sub(r'sqrt\((\w+)', r'sqrt(\1)', result)
            else:
                result = re.sub(pattern, f' {symbol} ', result)
        
        return result
    
    def _convert_variables(self, text: str) -> str:
        """Convert variable phrases to variable symbols."""
        result = text
        
        for phrase, variable in self.variable_phrases.items():
            pattern = r'\b' + re.escape(phrase) + r'\b'
            result = re.sub(pattern, variable, result)
        
        return result
    
    def _convert_equations(self, text: str) -> str:
        """Convert equation phrases to equals signs."""
        result = text
        
        for phrase, symbol in self.equation_phrases.items():
            pattern = r'\b' + re.escape(phrase) + r'\b'
            result = re.sub(pattern, f' {symbol} ', result)
        
        return result
    
    def _extract_math_expression(self, text: str) -> str:
        """Extract the mathematical expression from converted text."""
        # Remove common non-mathematical words
        stop_words = [
            'find', 'solve', 'what', 'calculate', 'determine', 'compute',
            'the', 'a', 'an', 'and', 'or', 'but', 'if', 'then', 'when'
        ]
        
        words = text.split()
        filtered_words = []
        
        for word in words:
            if word not in stop_words and (
                word.isdigit() or 
                word in ['+', '-', '*', '/', '=', '(', ')', '**'] or
                re.match(r'^[a-z]$', word) or  # Single letter variables
                word.startswith('sqrt(') or
                word.replace('.', '').isdigit()  # Decimal numbers
            ):
                filtered_words.append(word)
        
        # Join and clean up spacing around operators
        expression = ' '.join(filtered_words)
        expression = re.sub(r'\s*([+\-*/=()])\s*', r'\1', expression)
        expression = re.sub(r'\s+', ' ', expression).strip()
        
        return expression
    
    def _identify_context(self, text: str) -> Dict[str, Any]:
        """Identify the mathematical context and problem type."""
        context = {
            'domain': 'algebra',  # default
            'operation_type': 'solve',
            'keywords': [],
            'complexity': 'basic'
        }
        
        text_lower = text.lower()
        
        # Identify domain
        if any(word in text_lower for word in ['derivative', 'differentiate', 'rate of change']):
            context['domain'] = 'calculus'
            context['operation_type'] = 'differentiate'
        elif any(word in text_lower for word in ['integral', 'integrate', 'area under']):
            context['domain'] = 'calculus'
            context['operation_type'] = 'integrate'
        elif any(word in text_lower for word in ['limit', 'approaches']):
            context['domain'] = 'calculus'
            context['operation_type'] = 'limit'
        elif any(word in text_lower for word in ['probability', 'chance', 'odds']):
            context['domain'] = 'statistics'
            context['operation_type'] = 'probability'
        elif any(word in text_lower for word in ['mean', 'average', 'median', 'standard deviation']):
            context['domain'] = 'statistics'
            context['operation_type'] = 'descriptive'
        
        # Extract keywords
        math_keywords = [
            'solve', 'find', 'calculate', 'determine', 'evaluate',
            'simplify', 'factor', 'expand', 'graph', 'plot'
        ]
        
        for keyword in math_keywords:
            if keyword in text_lower:
                context['keywords'].append(keyword)
        
        # Assess complexity
        if any(word in text_lower for word in ['system', 'multiple', 'simultaneous']):
            context['complexity'] = 'intermediate'
        elif any(word in text_lower for word in ['optimization', 'maximum', 'minimum']):
            context['complexity'] = 'advanced'
        
        return context
    
    def extract_word_problem_data(self, text: str) -> Dict[str, Any]:
        """
        Extract numerical data and relationships from word problems.
        
        Args:
            text: Word problem text
            
        Returns:
            Dictionary containing extracted data and relationships
        """
        try:
            # Extract numbers from text
            numbers = self._extract_numbers(text)
            
            # Extract relationships
            relationships = self._extract_relationships(text)
            
            # Extract unknowns
            unknowns = self._extract_unknowns(text)
            
            # Extract constraints
            constraints = self._extract_constraints(text)
            
            return {
                'numbers': numbers,
                'relationships': relationships,
                'unknowns': unknowns,
                'constraints': constraints,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def _extract_numbers(self, text: str) -> List[Dict[str, Any]]:
        """Extract numbers and their context from text."""
        numbers = []
        
        # Find all numbers (including decimals)
        number_pattern = r'\b\d+(?:\.\d+)?\b'
        matches = re.finditer(number_pattern, text)
        
        for match in matches:
            number = float(match.group()) if '.' in match.group() else int(match.group())
            start, end = match.span()
            
            # Get context (words before and after)
            words = text.split()
            word_index = len(text[:start].split()) - 1
            
            context_before = words[max(0, word_index-2):word_index]
            context_after = words[word_index+1:word_index+3]
            
            numbers.append({
                'value': number,
                'position': (start, end),
                'context_before': context_before,
                'context_after': context_after
            })
        
        return numbers
    
    def _extract_relationships(self, text: str) -> List[str]:
        """Extract mathematical relationships from text."""
        relationships = []
        
        relationship_patterns = [
            r'(\w+)\s+is\s+(\w+)\s+more\s+than\s+(\w+)',
            r'(\w+)\s+is\s+(\w+)\s+less\s+than\s+(\w+)',
            r'(\w+)\s+is\s+(\w+)\s+times\s+(\w+)',
            r'(\w+)\s+equals\s+(\w+)',
            r'the\s+sum\s+of\s+(\w+)\s+and\s+(\w+)',
            r'the\s+difference\s+between\s+(\w+)\s+and\s+(\w+)'
        ]
        
        for pattern in relationship_patterns:
            matches = re.finditer(pattern, text.lower())
            for match in matches:
                relationships.append(match.group())
        
        return relationships
    
    def _extract_unknowns(self, text: str) -> List[str]:
        """Extract unknown variables from text."""
        unknowns = []
        
        unknown_patterns = [
            r'find\s+(\w+)',
            r'what\s+is\s+(\w+)',
            r'solve\s+for\s+(\w+)',
            r'(\w+)\s+is\s+unknown',
            r'let\s+(\w+)\s+be'
        ]
        
        for pattern in unknown_patterns:
            matches = re.finditer(pattern, text.lower())
            for match in matches:
                unknown = match.group(1)
                if unknown not in unknowns:
                    unknowns.append(unknown)
        
        return unknowns
    
    def _extract_constraints(self, text: str) -> List[str]:
        """Extract constraints and conditions from text."""
        constraints = []
        
        constraint_patterns = [
            r'(\w+)\s+must\s+be\s+(\w+)',
            r'(\w+)\s+cannot\s+be\s+(\w+)',
            r'(\w+)\s+is\s+greater\s+than\s+(\w+)',
            r'(\w+)\s+is\s+less\s+than\s+(\w+)',
            r'(\w+)\s+is\s+at\s+least\s+(\w+)',
            r'(\w+)\s+is\s+at\s+most\s+(\w+)'
        ]
        
        for pattern in constraint_patterns:
            matches = re.finditer(pattern, text.lower())
            for match in matches:
                constraints.append(match.group())
        
        return constraints
