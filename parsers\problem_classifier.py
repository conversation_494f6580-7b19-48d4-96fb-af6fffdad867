"""
Problem Classifier Module

Classifies mathematical problems into categories (algebra, calculus, statistics, etc.)
and determines the appropriate solving approach.
"""

import re
from typing import Dict, Any, List, Tuple
from enum import Enum


class ProblemType(Enum):
    """Enumeration of mathematical problem types."""
    ALGEBRA_EQUATION = "algebra_equation"
    ALGEBRA_SYSTEM = "algebra_system"
    ALGEBRA_INEQUALITY = "algebra_inequality"
    ALGEBRA_POLYNOMIAL = "algebra_polynomial"
    CALCULUS_DERIVATIVE = "calculus_derivative"
    CALCULUS_INTEGRAL = "calculus_integral"
    CALCULUS_LIMIT = "calculus_limit"
    STATISTICS_DESCRIPTIVE = "statistics_descriptive"
    STATISTICS_PROBABILITY = "statistics_probability"
    STATISTICS_HYPOTHESIS = "statistics_hypothesis"
    TRIGONOMETRY = "trigonometry"
    LINEAR_ALGEBRA = "linear_algebra"
    UNKNOWN = "unknown"


class ProblemClassifier:
    """Classifies mathematical problems and determines solving approach."""
    
    def __init__(self):
        self.keywords = {
            ProblemType.CALCULUS_DERIVATIVE: [
                'derivative', 'differentiate', 'find dy/dx', 'find d/dx',
                'rate of change', 'slope', 'tangent', "f'", 'prime'
            ],
            ProblemType.CALCULUS_INTEGRAL: [
                'integral', 'integrate', 'antiderivative', 'area under',
                'definite integral', 'indefinite integral', '∫'
            ],
            ProblemType.CALCULUS_LIMIT: [
                'limit', 'lim', 'approaches', 'as x approaches',
                'continuity', 'discontinuity'
            ],
            ProblemType.STATISTICS_DESCRIPTIVE: [
                'mean', 'average', 'median', 'mode', 'standard deviation',
                'variance', 'range', 'quartile', 'percentile'
            ],
            ProblemType.STATISTICS_PROBABILITY: [
                'probability', 'chance', 'odds', 'binomial', 'normal distribution',
                'poisson', 'random variable', 'expected value'
            ],
            ProblemType.STATISTICS_HYPOTHESIS: [
                'hypothesis test', 't-test', 'z-test', 'chi-square',
                'null hypothesis', 'alternative hypothesis', 'p-value'
            ],
            ProblemType.ALGEBRA_SYSTEM: [
                'system of equations', 'simultaneous equations',
                'multiple equations', 'solve system'
            ],
            ProblemType.ALGEBRA_INEQUALITY: [
                'inequality', 'greater than', 'less than', '>', '<',
                '≥', '≤', 'solve inequality'
            ],
            ProblemType.TRIGONOMETRY: [
                'sin', 'cos', 'tan', 'trigonometric', 'triangle',
                'angle', 'radian', 'degree'
            ]
        }
        
        self.patterns = {
            ProblemType.CALCULUS_DERIVATIVE: [
                r'd/dx\s*\(',
                r'f\'',
                r'derivative\s+of',
                r'differentiate'
            ],
            ProblemType.CALCULUS_INTEGRAL: [
                r'∫',
                r'integral\s+of',
                r'integrate',
                r'antiderivative'
            ],
            ProblemType.CALCULUS_LIMIT: [
                r'lim\s*\(',
                r'limit\s+as',
                r'approaches'
            ],
            ProblemType.ALGEBRA_EQUATION: [
                r'=',
                r'solve\s+for',
                r'find\s+x'
            ],
            ProblemType.ALGEBRA_INEQUALITY: [
                r'[<>≤≥]',
                r'inequality'
            ]
        }
    
    def classify_problem(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Classify a mathematical problem.
        
        Args:
            problem_text: Natural language description or mathematical expression
            parsed_data: Optional parsed mathematical data
            
        Returns:
            Classification result with problem type and confidence
        """
        problem_text_lower = problem_text.lower()
        
        # Score each problem type
        type_scores = {}
        
        # Check keywords
        for problem_type, keywords in self.keywords.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in problem_text_lower:
                    score += 1
            type_scores[problem_type] = score
        
        # Check patterns
        for problem_type, patterns in self.patterns.items():
            for pattern in patterns:
                if re.search(pattern, problem_text, re.IGNORECASE):
                    type_scores[problem_type] = type_scores.get(problem_type, 0) + 2
        
        # Analyze parsed mathematical data if available
        if parsed_data and parsed_data.get('success'):
            math_scores = self._analyze_mathematical_structure(parsed_data)
            for problem_type, score in math_scores.items():
                type_scores[problem_type] = type_scores.get(problem_type, 0) + score
        
        # Determine best match
        if not type_scores or max(type_scores.values()) == 0:
            best_type = ProblemType.UNKNOWN
            confidence = 0.0
        else:
            best_type = max(type_scores, key=type_scores.get)
            max_score = type_scores[best_type]
            total_score = sum(type_scores.values())
            confidence = max_score / total_score if total_score > 0 else 0.0
        
        # Get solving approach
        approach = self._get_solving_approach(best_type, problem_text, parsed_data)
        
        return {
            'problem_type': best_type,
            'confidence': confidence,
            'type_scores': type_scores,
            'solving_approach': approach,
            'recommendations': self._get_recommendations(best_type, problem_text)
        }
    
    def _analyze_mathematical_structure(self, parsed_data: Dict[str, Any]) -> Dict[ProblemType, int]:
        """Analyze mathematical structure to help classify problem."""
        scores = {}
        
        # Check for equations
        if 'equation' in parsed_data or '=' in parsed_data.get('original', ''):
            scores[ProblemType.ALGEBRA_EQUATION] = 3
        
        # Check for multiple equations (system)
        if isinstance(parsed_data.get('parsed_equations'), list):
            scores[ProblemType.ALGEBRA_SYSTEM] = 4
        
        # Check for inequalities
        if any(op in parsed_data.get('original', '') for op in ['<', '>', '≤', '≥']):
            scores[ProblemType.ALGEBRA_INEQUALITY] = 3
        
        # Check for calculus functions
        functions = parsed_data.get('functions', [])
        if any(func in ['sin', 'cos', 'tan', 'sec', 'csc', 'cot'] for func in functions):
            scores[ProblemType.TRIGONOMETRY] = 2
        
        if any(func in ['log', 'exp', 'sqrt'] for func in functions):
            scores[ProblemType.CALCULUS_DERIVATIVE] = 1
            scores[ProblemType.CALCULUS_INTEGRAL] = 1
        
        # Check for polynomial degree
        if 'parsed' in parsed_data:
            try:
                expr = parsed_data['parsed']
                variables = parsed_data.get('variables', [])
                if variables:
                    var = variables[0]  # Use first variable
                    if hasattr(expr, 'as_poly'):
                        poly = expr.as_poly(var)
                        if poly and poly.degree() > 1:
                            scores[ProblemType.ALGEBRA_POLYNOMIAL] = 2
            except:
                pass
        
        return scores
    
    def _get_solving_approach(self, problem_type: ProblemType, 
                            problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Determine the appropriate solving approach for the problem type."""
        
        approaches = {
            ProblemType.ALGEBRA_EQUATION: {
                'solver': 'AlgebraSolver',
                'method': 'solve_equation',
                'parameters': self._extract_algebra_params(problem_text, parsed_data)
            },
            ProblemType.ALGEBRA_SYSTEM: {
                'solver': 'AlgebraSolver',
                'method': 'solve_system',
                'parameters': self._extract_system_params(problem_text, parsed_data)
            },
            ProblemType.ALGEBRA_POLYNOMIAL: {
                'solver': 'AlgebraSolver',
                'method': 'factor_polynomial',
                'parameters': self._extract_polynomial_params(problem_text, parsed_data)
            },
            ProblemType.CALCULUS_DERIVATIVE: {
                'solver': 'CalculusSolver',
                'method': 'differentiate',
                'parameters': self._extract_derivative_params(problem_text, parsed_data)
            },
            ProblemType.CALCULUS_INTEGRAL: {
                'solver': 'CalculusSolver',
                'method': 'integrate',
                'parameters': self._extract_integral_params(problem_text, parsed_data)
            },
            ProblemType.CALCULUS_LIMIT: {
                'solver': 'CalculusSolver',
                'method': 'compute_limit',
                'parameters': self._extract_limit_params(problem_text, parsed_data)
            },
            ProblemType.STATISTICS_DESCRIPTIVE: {
                'solver': 'StatisticsSolver',
                'method': 'descriptive_statistics',
                'parameters': self._extract_stats_params(problem_text, parsed_data)
            },
            ProblemType.STATISTICS_PROBABILITY: {
                'solver': 'StatisticsSolver',
                'method': 'probability_calculation',
                'parameters': self._extract_probability_params(problem_text, parsed_data)
            }
        }
        
        return approaches.get(problem_type, {
            'solver': 'AlgebraSolver',
            'method': 'solve_equation',
            'parameters': {}
        })
    
    def _extract_algebra_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for algebra solving."""
        params = {}
        
        if parsed_data and parsed_data.get('success'):
            if 'equation' in parsed_data:
                params['equation_str'] = parsed_data['original']
                if parsed_data.get('variables'):
                    params['variable'] = parsed_data['variables'][0]
        
        return params
    
    def _extract_system_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for system solving."""
        params = {}
        
        if parsed_data and parsed_data.get('success'):
            if 'original_equations' in parsed_data:
                params['equations'] = parsed_data['original_equations']
                params['variables'] = parsed_data.get('variables', [])
        
        return params
    
    def _extract_polynomial_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for polynomial operations."""
        params = {}
        
        if parsed_data and parsed_data.get('success'):
            params['expression_str'] = parsed_data['original']
        
        return params
    
    def _extract_derivative_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for derivative calculation."""
        params = {}
        
        if parsed_data and parsed_data.get('success'):
            params['expression_str'] = parsed_data['original']
            if parsed_data.get('variables'):
                params['variable'] = parsed_data['variables'][0]
        
        # Check for order specification
        order_match = re.search(r'(\d+)(st|nd|rd|th)\s+derivative', problem_text.lower())
        if order_match:
            params['order'] = int(order_match.group(1))
        
        return params
    
    def _extract_integral_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for integral calculation."""
        params = {}
        
        if parsed_data and parsed_data.get('success'):
            params['expression_str'] = parsed_data['original']
            if parsed_data.get('variables'):
                params['variable'] = parsed_data['variables'][0]
        
        # Check for definite integral bounds
        bounds_match = re.search(r'from\s+([^to]+)\s+to\s+([^,\s]+)', problem_text.lower())
        if bounds_match:
            params['definite'] = True
            params['lower_limit'] = bounds_match.group(1).strip()
            params['upper_limit'] = bounds_match.group(2).strip()
        
        return params
    
    def _extract_limit_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for limit calculation."""
        params = {}
        
        if parsed_data and parsed_data.get('success'):
            params['expression_str'] = parsed_data['original']
            if parsed_data.get('variables'):
                params['variable'] = parsed_data['variables'][0]
        
        # Extract approach value
        approach_match = re.search(r'approaches\s+([^,\s]+)', problem_text.lower())
        if approach_match:
            params['approach_value'] = approach_match.group(1).strip()
        
        return params
    
    def _extract_stats_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for statistics calculation."""
        params = {}
        
        # Look for data in the problem text
        data_match = re.search(r'data[:\s]+([0-9,.\s]+)', problem_text.lower())
        if data_match:
            data_str = data_match.group(1)
            try:
                data = [float(x.strip()) for x in data_str.replace(',', ' ').split() if x.strip()]
                params['data'] = data
            except:
                pass
        
        return params
    
    def _extract_probability_params(self, problem_text: str, parsed_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract parameters for probability calculation."""
        params = {}
        
        # Detect probability distribution type
        if 'binomial' in problem_text.lower():
            params['prob_type'] = 'binomial'
            # Extract n, k, p parameters
            n_match = re.search(r'n\s*=\s*(\d+)', problem_text.lower())
            k_match = re.search(r'k\s*=\s*(\d+)', problem_text.lower())
            p_match = re.search(r'p\s*=\s*([\d.]+)', problem_text.lower())
            
            if n_match:
                params['n'] = int(n_match.group(1))
            if k_match:
                params['k'] = int(k_match.group(1))
            if p_match:
                params['p'] = float(p_match.group(1))
        
        elif 'normal' in problem_text.lower():
            params['prob_type'] = 'normal'
            # Extract mean, std_dev parameters
            mean_match = re.search(r'mean\s*=\s*([\d.-]+)', problem_text.lower())
            std_match = re.search(r'std\s*=\s*([\d.]+)', problem_text.lower())
            
            if mean_match:
                params['mean'] = float(mean_match.group(1))
            if std_match:
                params['std_dev'] = float(std_match.group(1))
        
        return params
    
    def _get_recommendations(self, problem_type: ProblemType, problem_text: str) -> List[str]:
        """Get recommendations for solving the problem."""
        recommendations = {
            ProblemType.ALGEBRA_EQUATION: [
                "Ensure the equation is properly formatted with an equals sign",
                "Specify which variable to solve for if there are multiple variables"
            ],
            ProblemType.CALCULUS_DERIVATIVE: [
                "Specify the variable to differentiate with respect to",
                "Indicate the order of derivative if higher than first"
            ],
            ProblemType.CALCULUS_INTEGRAL: [
                "Specify if you want a definite or indefinite integral",
                "For definite integrals, provide the limits of integration"
            ],
            ProblemType.STATISTICS_DESCRIPTIVE: [
                "Provide the dataset as a list of numbers",
                "Specify which statistics you want to calculate"
            ],
            ProblemType.UNKNOWN: [
                "Try to be more specific about what you want to solve",
                "Use standard mathematical notation",
                "Specify the type of problem (algebra, calculus, statistics, etc.)"
            ]
        }
        
        return recommendations.get(problem_type, [
            "Provide more context about the problem",
            "Use clear mathematical notation"
        ])
