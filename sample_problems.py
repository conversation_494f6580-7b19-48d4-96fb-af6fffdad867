"""
Sample Problems for Math Explainer Platform

Collection of sample mathematical problems for testing and demonstration.
"""

# Sample problems organized by category
SAMPLE_PROBLEMS = {
    "algebra": {
        "linear_equations": [
            "2*x + 3 = 7",
            "5*x - 2 = 3*x + 4",
            "x/2 + 3 = x/3 + 5"
        ],
        "quadratic_equations": [
            "x**2 + 2*x - 3 = 0",
            "2*x**2 - 5*x + 2 = 0",
            "x**2 - 4*x + 4 = 0"
        ],
        "systems": [
            ["2*x + 3*y = 7", "x - y = 1"],
            ["x + y = 5", "2*x - y = 1"],
            ["3*x + 2*y = 12", "x - y = 1"]
        ],
        "polynomials": [
            "x**3 - 6*x**2 + 11*x - 6",
            "x**4 - 5*x**2 + 4",
            "x**2 - 9"
        ]
    },
    
    "calculus": {
        "derivatives": [
            "x**2 + 3*x - 1",
            "sin(x) + cos(x)",
            "x*exp(x)",
            "log(x**2 + 1)",
            "x**3 * sin(x)"
        ],
        "integrals": [
            "2*x + 3",
            "x**2 - 4*x + 1",
            "sin(x)",
            "1/x",
            "exp(x)"
        ],
        "definite_integrals": [
            ("x**2", "0", "1"),
            ("sin(x)", "0", "pi"),
            ("1/(1+x**2)", "-1", "1")
        ],
        "limits": [
            ("sin(x)/x", "x", "0"),
            ("(x**2 - 1)/(x - 1)", "x", "1"),
            ("(1 + 1/x)**x", "x", "oo")
        ]
    },
    
    "statistics": {
        "descriptive": [
            [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            [23, 45, 67, 89, 12, 34, 56, 78, 90, 11],
            [100, 105, 98, 102, 97, 103, 99, 101, 96, 104]
        ],
        "probability": {
            "binomial": [
                {"n": 10, "k": 3, "p": 0.5},
                {"n": 20, "k": 8, "p": 0.4},
                {"n": 15, "k": 5, "p": 0.3}
            ],
            "normal": [
                {"mean": 0, "std_dev": 1, "x": 1.96},
                {"mean": 100, "std_dev": 15, "x": 115},
                {"mean": 50, "std_dev": 10, "x": 45}
            ],
            "poisson": [
                {"lambda": 2, "k": 3},
                {"lambda": 5, "k": 7},
                {"lambda": 1.5, "k": 2}
            ]
        }
    }
}

# Natural language problems
NATURAL_LANGUAGE_PROBLEMS = [
    "Find the derivative of x squared plus three x minus one",
    "Solve x squared minus five x plus six equals zero",
    "What is the integral of two x plus three",
    "Calculate the limit of sine x over x as x approaches zero",
    "Factor x squared minus nine",
    "Expand the expression (x plus two) times (x minus three)",
    "Find the mean and standard deviation of the numbers 1, 2, 3, 4, 5",
    "What is the probability of getting exactly 3 heads in 10 coin flips",
    "Solve the system: two x plus three y equals seven, x minus y equals one"
]

# Word problems
WORD_PROBLEMS = [
    "A ball is thrown upward with an initial velocity of 20 m/s. Its height h(t) = -5t² + 20t + 2. When does it hit the ground?",
    "The population of a city grows according to P(t) = 10000 * e^(0.02t). What is the growth rate at t = 5 years?",
    "A rectangular garden has a perimeter of 40 meters. If the length is 2 meters more than the width, find the dimensions.",
    "The cost function for producing x items is C(x) = 100 + 5x + 0.01x². Find the marginal cost when x = 50.",
    "A survey of 100 students found that 60 like math, 70 like science, and 40 like both. How many like neither?"
]

def get_sample_problem(category: str, subcategory: str = None, index: int = 0):
    """
    Get a sample problem from the collection.
    
    Args:
        category: Main category (algebra, calculus, statistics)
        subcategory: Subcategory within the main category
        index: Index of the problem within the subcategory
        
    Returns:
        Sample problem or None if not found
    """
    try:
        if category not in SAMPLE_PROBLEMS:
            return None
        
        category_problems = SAMPLE_PROBLEMS[category]
        
        if subcategory is None:
            # Return first available subcategory
            subcategory = list(category_problems.keys())[0]
        
        if subcategory not in category_problems:
            return None
        
        problems = category_problems[subcategory]
        
        if index >= len(problems):
            return None
        
        return problems[index]
    
    except (KeyError, IndexError):
        return None

def get_random_problem(category: str = None):
    """
    Get a random problem from the collection.
    
    Args:
        category: Optional category filter
        
    Returns:
        Random problem
    """
    import random
    
    if category and category in SAMPLE_PROBLEMS:
        category_problems = SAMPLE_PROBLEMS[category]
        subcategory = random.choice(list(category_problems.keys()))
        problems = category_problems[subcategory]
        return random.choice(problems)
    else:
        # Choose from all problems
        all_problems = []
        for cat in SAMPLE_PROBLEMS.values():
            for subcat in cat.values():
                if isinstance(subcat, list):
                    all_problems.extend(subcat)
                elif isinstance(subcat, dict):
                    for prob_list in subcat.values():
                        all_problems.extend(prob_list)
        
        return random.choice(all_problems)

def get_natural_language_problem(index: int = None):
    """
    Get a natural language problem.
    
    Args:
        index: Optional index, random if None
        
    Returns:
        Natural language problem
    """
    import random
    
    if index is None:
        return random.choice(NATURAL_LANGUAGE_PROBLEMS)
    elif 0 <= index < len(NATURAL_LANGUAGE_PROBLEMS):
        return NATURAL_LANGUAGE_PROBLEMS[index]
    else:
        return None

def get_word_problem(index: int = None):
    """
    Get a word problem.
    
    Args:
        index: Optional index, random if None
        
    Returns:
        Word problem
    """
    import random
    
    if index is None:
        return random.choice(WORD_PROBLEMS)
    elif 0 <= index < len(WORD_PROBLEMS):
        return WORD_PROBLEMS[index]
    else:
        return None

def list_all_categories():
    """List all available categories and subcategories."""
    categories = {}
    
    for category, subcategories in SAMPLE_PROBLEMS.items():
        categories[category] = list(subcategories.keys())
    
    return categories

def count_problems():
    """Count total number of problems in each category."""
    counts = {}
    
    for category, subcategories in SAMPLE_PROBLEMS.items():
        category_count = 0
        for subcategory, problems in subcategories.items():
            if isinstance(problems, list):
                category_count += len(problems)
            elif isinstance(problems, dict):
                for prob_list in problems.values():
                    category_count += len(prob_list)
        counts[category] = category_count
    
    counts['natural_language'] = len(NATURAL_LANGUAGE_PROBLEMS)
    counts['word_problems'] = len(WORD_PROBLEMS)
    
    return counts

if __name__ == "__main__":
    # Demo the sample problems
    print("Math Explainer Platform - Sample Problems")
    print("=" * 50)
    
    # Show categories
    print("\nAvailable Categories:")
    categories = list_all_categories()
    for category, subcategories in categories.items():
        print(f"  {category.title()}: {', '.join(subcategories)}")
    
    # Show problem counts
    print("\nProblem Counts:")
    counts = count_problems()
    for category, count in counts.items():
        print(f"  {category.replace('_', ' ').title()}: {count} problems")
    
    # Show some sample problems
    print("\nSample Problems:")
    
    print("\n1. Algebra - Linear Equation:")
    print(f"   {get_sample_problem('algebra', 'linear_equations', 0)}")
    
    print("\n2. Calculus - Derivative:")
    print(f"   d/dx({get_sample_problem('calculus', 'derivatives', 0)})")
    
    print("\n3. Statistics - Descriptive:")
    data = get_sample_problem('statistics', 'descriptive', 0)
    print(f"   Calculate statistics for: {data}")
    
    print("\n4. Natural Language:")
    print(f"   {get_natural_language_problem(0)}")
    
    print("\n5. Word Problem:")
    print(f"   {get_word_problem(0)}")
    
    print("\n6. Random Problem:")
    print(f"   {get_random_problem()}")
