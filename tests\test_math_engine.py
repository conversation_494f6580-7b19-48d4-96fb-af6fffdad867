"""
Test Math Engine Components

Tests for algebra solver, calculus solver, and statistics solver.
"""

import unittest
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from math_engine import AlgebraSolver, CalculusSolver, StatisticsSolver
import sympy as sp
import numpy as np


class TestAlgebraSolver(unittest.TestCase):
    """Test cases for AlgebraSolver."""
    
    def setUp(self):
        self.solver = AlgebraSolver()
    
    def test_solve_linear_equation(self):
        """Test solving linear equations."""
        result = self.solver.solve_equation("2*x + 3 = 7")
        
        self.assertTrue(result['success'])
        self.assertEqual(len(result['solutions']), 1)
        self.assertEqual(result['solutions'][0], 2)
    
    def test_solve_quadratic_equation(self):
        """Test solving quadratic equations."""
        result = self.solver.solve_equation("x**2 - 5*x + 6 = 0")
        
        self.assertTrue(result['success'])
        self.assertEqual(len(result['solutions']), 2)
        self.assertIn(2, result['solutions'])
        self.assertIn(3, result['solutions'])
    
    def test_solve_system_of_equations(self):
        """Test solving system of equations."""
        equations = ["2*x + 3*y = 7", "x - y = 1"]
        result = self.solver.solve_system(equations)
        
        self.assertTrue(result['success'])
        self.assertIn('x', result['solutions'])
        self.assertIn('y', result['solutions'])
    
    def test_factor_polynomial(self):
        """Test polynomial factoring."""
        result = self.solver.factor_polynomial("x**2 - 4")
        
        self.assertTrue(result['success'])
        # Should factor to (x-2)(x+2)
        factored = result['result']
        expanded = sp.expand(factored)
        original = sp.sympify("x**2 - 4")
        self.assertEqual(expanded, original)
    
    def test_expand_expression(self):
        """Test expression expansion."""
        result = self.solver.expand_expression("(x + 2)*(x - 3)")
        
        self.assertTrue(result['success'])
        expected = sp.sympify("x**2 - x - 6")
        self.assertEqual(result['result'], expected)


class TestCalculusSolver(unittest.TestCase):
    """Test cases for CalculusSolver."""
    
    def setUp(self):
        self.solver = CalculusSolver()
    
    def test_differentiate_polynomial(self):
        """Test differentiation of polynomials."""
        result = self.solver.differentiate("x**3 + 2*x**2 - x + 1")
        
        self.assertTrue(result['success'])
        expected = sp.sympify("3*x**2 + 4*x - 1")
        self.assertEqual(result['result'], expected)
    
    def test_differentiate_trigonometric(self):
        """Test differentiation of trigonometric functions."""
        result = self.solver.differentiate("sin(x)")
        
        self.assertTrue(result['success'])
        expected = sp.cos(sp.Symbol('x'))
        self.assertEqual(result['result'], expected)
    
    def test_integrate_polynomial(self):
        """Test integration of polynomials."""
        result = self.solver.integrate("2*x + 3")
        
        self.assertTrue(result['success'])
        # Check if derivative of result gives original
        derivative = sp.diff(result['result'], sp.Symbol('x'))
        original = sp.sympify("2*x + 3")
        self.assertEqual(derivative, original)
    
    def test_definite_integral(self):
        """Test definite integration."""
        result = self.solver.integrate("x**2", definite=True, lower_limit="0", upper_limit="1")
        
        self.assertTrue(result['success'])
        # Integral of x^2 from 0 to 1 should be 1/3
        expected = sp.Rational(1, 3)
        self.assertEqual(result['result'], expected)
    
    def test_compute_limit(self):
        """Test limit computation."""
        result = self.solver.compute_limit("sin(x)/x", approach_value="0")
        
        self.assertTrue(result['success'])
        # Limit of sin(x)/x as x approaches 0 is 1
        self.assertEqual(result['result'], 1)


class TestStatisticsSolver(unittest.TestCase):
    """Test cases for StatisticsSolver."""
    
    def setUp(self):
        self.solver = StatisticsSolver()
    
    def test_descriptive_statistics(self):
        """Test descriptive statistics calculation."""
        data = [1, 2, 3, 4, 5]
        result = self.solver.descriptive_statistics(data)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['mean'], 3.0)
        self.assertEqual(result['median'], 3.0)
        self.assertEqual(result['min'], 1)
        self.assertEqual(result['max'], 5)
        self.assertEqual(result['range'], 4)
    
    def test_binomial_probability(self):
        """Test binomial probability calculation."""
        result = self.solver.probability_calculation('binomial', n=10, k=5, p=0.5)
        
        self.assertTrue(result['success'])
        # P(X=5) for Binomial(10, 0.5) should be approximately 0.246
        self.assertAlmostEqual(result['probability'], 0.24609375, places=6)
    
    def test_normal_probability(self):
        """Test normal probability calculation."""
        result = self.solver.probability_calculation('normal', mean=0, std_dev=1, x=0, prob_type='less_than')
        
        self.assertTrue(result['success'])
        # P(X < 0) for N(0,1) should be 0.5
        self.assertAlmostEqual(result['probability'], 0.5, places=2)
    
    def test_poisson_probability(self):
        """Test Poisson probability calculation."""
        result = self.solver.probability_calculation('poisson', lambda_param=2, k=2)
        
        self.assertTrue(result['success'])
        # P(X=2) for Poisson(2) should be approximately 0.271
        self.assertAlmostEqual(result['probability'], 0.2706705665, places=6)


class TestIntegration(unittest.TestCase):
    """Integration tests for the math engine."""
    
    def setUp(self):
        self.algebra_solver = AlgebraSolver()
        self.calculus_solver = CalculusSolver()
        self.stats_solver = StatisticsSolver()
    
    def test_solve_and_plot_quadratic(self):
        """Test solving quadratic and preparing for plotting."""
        # Solve equation
        result = self.algebra_solver.solve_equation("x**2 - 4*x + 3 = 0")
        
        self.assertTrue(result['success'])
        self.assertEqual(len(result['solutions']), 2)
        
        # Solutions should be 1 and 3
        solutions = sorted(result['solutions'])
        self.assertEqual(solutions, [1, 3])
    
    def test_calculus_chain(self):
        """Test calculus operations in sequence."""
        # Start with a function
        original = "x**3 - 3*x**2 + 2*x"
        
        # Differentiate
        derivative_result = self.calculus_solver.differentiate(original)
        self.assertTrue(derivative_result['success'])
        
        # Integrate the derivative (should get back close to original)
        integral_result = self.calculus_solver.integrate(str(derivative_result['result']))
        self.assertTrue(integral_result['success'])
        
        # The integral should differ from original only by a constant
        diff = sp.diff(integral_result['result'] - sp.sympify(original), sp.Symbol('x'))
        self.assertEqual(diff, 0)
    
    def test_statistics_workflow(self):
        """Test complete statistics workflow."""
        # Generate some test data
        np.random.seed(42)  # For reproducible results
        data = np.random.normal(10, 2, 100).tolist()
        
        # Calculate descriptive statistics
        desc_result = self.stats_solver.descriptive_statistics(data)
        self.assertTrue(desc_result['success'])
        
        # Mean should be close to 10
        self.assertAlmostEqual(desc_result['mean'], 10, delta=1)
        
        # Standard deviation should be close to 2
        self.assertAlmostEqual(desc_result['std_dev'], 2, delta=1)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestAlgebraSolver))
    test_suite.addTest(unittest.makeSuite(TestCalculusSolver))
    test_suite.addTest(unittest.makeSuite(TestStatisticsSolver))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nTest Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # Exit with appropriate code
    exit_code = 0 if result.wasSuccessful() else 1
    exit(exit_code)
