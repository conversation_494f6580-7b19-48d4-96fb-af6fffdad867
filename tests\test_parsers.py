"""
Test Parser Components

Tests for math parser, problem classifier, and natural language parser.
"""

import unittest
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from parsers import <PERSON><PERSON><PERSON><PERSON>, ProblemClassifier, NaturalLanguageParser
from parsers.problem_classifier import ProblemType
import sympy as sp


class TestMathParser(unittest.TestCase):
    """Test cases for MathParser."""
    
    def setUp(self):
        self.parser = MathParser()
    
    def test_parse_simple_expression(self):
        """Test parsing simple mathematical expressions."""
        result = self.parser.parse_expression("x**2 + 2*x - 1")
        
        self.assertTrue(result['success'])
        self.assertEqual(str(result['parsed']), "x**2 + 2*x - 1")
        self.assertIn('x', result['variables'])
    
    def test_parse_equation(self):
        """Test parsing equations."""
        result = self.parser.parse_equation("x**2 + 2*x = 3")
        
        self.assertTrue(result['success'])
        self.assertIn('x', result['variables'])
        self.assertEqual(str(result['equation'].lhs), "x**2 + 2*x")
        self.assertEqual(str(result['equation'].rhs), "3")
    
    def test_parse_system(self):
        """Test parsing system of equations."""
        equations = ["2*x + 3*y = 7", "x - y = 1"]
        result = self.parser.parse_system(equations)
        
        self.assertTrue(result['success'])
        self.assertEqual(len(result['parsed_equations']), 2)
        self.assertIn('x', result['variables'])
        self.assertIn('y', result['variables'])
    
    def test_parse_trigonometric(self):
        """Test parsing trigonometric functions."""
        result = self.parser.parse_expression("sin(x) + cos(2*x)")
        
        self.assertTrue(result['success'])
        self.assertIn('sin', result['functions'])
        self.assertIn('cos', result['functions'])
    
    def test_validate_expression(self):
        """Test expression validation."""
        # Valid expression
        result = self.parser.validate_expression("x**2 + 1")
        self.assertTrue(result['valid'])
        
        # Invalid expression
        result = self.parser.validate_expression("x**2 + + 1")
        self.assertFalse(result['valid'])
        self.assertIn('suggestions', result)


class TestProblemClassifier(unittest.TestCase):
    """Test cases for ProblemClassifier."""
    
    def setUp(self):
        self.classifier = ProblemClassifier()
        self.parser = MathParser()
    
    def test_classify_algebra_equation(self):
        """Test classification of algebraic equations."""
        problem_text = "solve x^2 + 2*x - 3 = 0"
        parsed_data = self.parser.parse_equation("x^2 + 2*x - 3 = 0")
        
        result = self.classifier.classify_problem(problem_text, parsed_data)
        
        self.assertEqual(result['problem_type'], ProblemType.ALGEBRA_EQUATION)
        self.assertGreater(result['confidence'], 0.5)
    
    def test_classify_calculus_derivative(self):
        """Test classification of derivative problems."""
        problem_text = "find the derivative of x^2 + sin(x)"
        parsed_data = self.parser.parse_expression("x^2 + sin(x)")
        
        result = self.classifier.classify_problem(problem_text, parsed_data)
        
        self.assertEqual(result['problem_type'], ProblemType.CALCULUS_DERIVATIVE)
        self.assertGreater(result['confidence'], 0.5)
    
    def test_classify_calculus_integral(self):
        """Test classification of integral problems."""
        problem_text = "integrate x^2 dx"
        parsed_data = self.parser.parse_expression("x^2")
        
        result = self.classifier.classify_problem(problem_text, parsed_data)
        
        self.assertEqual(result['problem_type'], ProblemType.CALCULUS_INTEGRAL)
        self.assertGreater(result['confidence'], 0.5)
    
    def test_classify_statistics(self):
        """Test classification of statistics problems."""
        problem_text = "calculate the mean and standard deviation of the data"
        
        result = self.classifier.classify_problem(problem_text)
        
        self.assertEqual(result['problem_type'], ProblemType.STATISTICS_DESCRIPTIVE)
        self.assertGreater(result['confidence'], 0.5)
    
    def test_classify_system_equations(self):
        """Test classification of system of equations."""
        problem_text = "solve the system of equations"
        equations = ["2*x + 3*y = 7", "x - y = 1"]
        parsed_data = self.parser.parse_system(equations)
        
        result = self.classifier.classify_problem(problem_text, parsed_data)
        
        self.assertEqual(result['problem_type'], ProblemType.ALGEBRA_SYSTEM)
        self.assertGreater(result['confidence'], 0.5)


class TestNaturalLanguageParser(unittest.TestCase):
    """Test cases for NaturalLanguageParser."""
    
    def setUp(self):
        self.parser = NaturalLanguageParser()
    
    def test_parse_simple_arithmetic(self):
        """Test parsing simple arithmetic in natural language."""
        text = "two plus three equals five"
        result = self.parser.parse_natural_language(text)
        
        self.assertTrue(result['success'])
        # Should convert to something like "2 + 3 = 5"
        self.assertIn('+', result['converted_text'])
        self.assertIn('=', result['converted_text'])
    
    def test_parse_equation_language(self):
        """Test parsing equation language."""
        text = "x squared plus two x equals three"
        result = self.parser.parse_natural_language(text)
        
        self.assertTrue(result['success'])
        # Should identify algebraic context
        self.assertEqual(result['problem_context']['domain'], 'algebra')
    
    def test_parse_calculus_language(self):
        """Test parsing calculus language."""
        text = "find the derivative of x squared"
        result = self.parser.parse_natural_language(text)
        
        self.assertTrue(result['success'])
        # Should identify calculus context
        self.assertEqual(result['problem_context']['domain'], 'calculus')
        self.assertEqual(result['problem_context']['operation_type'], 'differentiate')
    
    def test_convert_words_to_numbers(self):
        """Test word-to-number conversion."""
        text = "twenty five plus thirty seven"
        result = self.parser.parse_natural_language(text)
        
        self.assertTrue(result['success'])
        # Should convert words to numbers
        converted = result['converted_text']
        self.assertIn('25', converted)
        self.assertIn('37', converted)
    
    def test_extract_word_problem_data(self):
        """Test extraction of data from word problems."""
        text = "John has 5 apples and Mary has 3 apples. How many apples do they have together?"
        result = self.parser.extract_word_problem_data(text)
        
        self.assertTrue(result['success'])
        # Should extract numbers
        self.assertTrue(len(result['numbers']) >= 2)
        # Should find numbers 5 and 3
        values = [num['value'] for num in result['numbers']]
        self.assertIn(5, values)
        self.assertIn(3, values)


class TestParserIntegration(unittest.TestCase):
    """Integration tests for parser components."""
    
    def setUp(self):
        self.math_parser = MathParser()
        self.classifier = ProblemClassifier()
        self.nl_parser = NaturalLanguageParser()
    
    def test_full_parsing_pipeline(self):
        """Test complete parsing pipeline from natural language to classification."""
        # Start with natural language
        text = "solve the equation x squared minus four equals zero"
        
        # Parse natural language
        nl_result = self.nl_parser.parse_natural_language(text)
        self.assertTrue(nl_result['success'])
        
        # Parse the mathematical expression
        math_expr = nl_result['math_expression']
        if math_expr:
            math_result = self.math_parser.parse_equation(math_expr)
            
            if math_result['success']:
                # Classify the problem
                classification = self.classifier.classify_problem(text, math_result)
                
                # Should classify as algebra equation
                self.assertEqual(classification['problem_type'], ProblemType.ALGEBRA_EQUATION)
                self.assertGreater(classification['confidence'], 0.3)
    
    def test_error_handling(self):
        """Test error handling in parsers."""
        # Test invalid mathematical expression
        result = self.math_parser.parse_expression("x + + y")
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        
        # Test invalid equation
        result = self.math_parser.parse_equation("x + y")  # No equals sign
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        
        # Test empty natural language
        result = self.nl_parser.parse_natural_language("")
        # Should handle gracefully
        self.assertIn('success', result)
    
    def test_complex_expressions(self):
        """Test parsing of complex mathematical expressions."""
        expressions = [
            "sin(x)**2 + cos(x)**2",
            "log(x) + exp(x)",
            "sqrt(x**2 + y**2)",
            "factorial(n)/(factorial(k)*factorial(n-k))"
        ]
        
        for expr in expressions:
            result = self.math_parser.parse_expression(expr)
            self.assertTrue(result['success'], f"Failed to parse: {expr}")
            self.assertIsNotNone(result['parsed'])
    
    def test_classification_accuracy(self):
        """Test classification accuracy for various problem types."""
        test_cases = [
            ("solve x^2 = 4", ProblemType.ALGEBRA_EQUATION),
            ("find derivative of sin(x)", ProblemType.CALCULUS_DERIVATIVE),
            ("integrate x^2 dx", ProblemType.CALCULUS_INTEGRAL),
            ("limit of sin(x)/x as x approaches 0", ProblemType.CALCULUS_LIMIT),
            ("calculate mean of data", ProblemType.STATISTICS_DESCRIPTIVE),
            ("binomial probability", ProblemType.STATISTICS_PROBABILITY)
        ]
        
        for problem_text, expected_type in test_cases:
            classification = self.classifier.classify_problem(problem_text)
            self.assertEqual(
                classification['problem_type'], 
                expected_type,
                f"Misclassified: '{problem_text}' as {classification['problem_type']} instead of {expected_type}"
            )


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestMathParser))
    test_suite.addTest(unittest.makeSuite(TestProblemClassifier))
    test_suite.addTest(unittest.makeSuite(TestNaturalLanguageParser))
    test_suite.addTest(unittest.makeSuite(TestParserIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nTest Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # Exit with appropriate code
    exit_code = 0 if result.wasSuccessful() else 1
    exit(exit_code)
