"""
Equation Visualizer Module

Specialized visualizations for equations, systems of equations, and algebraic relationships.
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import sympy as sp
from typing import Dict, Any, List, Tuple, Optional, Union


class EquationVisualizer:
    """Visualizer for equations and algebraic relationships."""
    
    def __init__(self):
        self.colors = px.colors.qualitative.Set1
    
    def plot_equation_solutions(self, equation, solutions: List, variable: str = 'x',
                              x_range: Tuple[float, float] = (-10, 10)) -> Dict[str, Any]:
        """
        Plot equation and highlight its solutions.
        
        Args:
            equation: SymPy equation or expression
            solutions: List of solution values
            variable: Variable name
            x_range: Range for plotting
            
        Returns:
            Dictionary containing plot figure
        """
        try:
            # Convert equation to expression (left side - right side = 0)
            if hasattr(equation, 'lhs') and hasattr(equation, 'rhs'):
                expr = equation.lhs - equation.rhs
            else:
                expr = equation
            
            # Create x values
            x_vals = np.linspace(x_range[0], x_range[1], 1000)
            
            # Convert to numerical function
            var = sp.Symbol(variable)
            f = sp.lambdify(var, expr, 'numpy')
            
            # Calculate y values
            y_vals = f(x_vals)
            
            # Create plot
            fig = go.Figure()
            
            # Plot the function
            fig.add_trace(go.Scatter(
                x=x_vals,
                y=y_vals,
                mode='lines',
                name=f'f({variable}) = {sp.latex(expr)}',
                line=dict(width=2, color='blue')
            ))
            
            # Add horizontal line at y=0
            fig.add_hline(y=0, line_dash="dash", line_color="gray", 
                         annotation_text="y = 0")
            
            # Highlight solutions
            for i, sol in enumerate(solutions):
                if isinstance(sol, (int, float, complex)):
                    sol_val = float(sol.real) if hasattr(sol, 'real') else float(sol)
                    
                    # Add vertical line at solution
                    fig.add_vline(
                        x=sol_val,
                        line_dash="dot",
                        line_color=self.colors[i % len(self.colors)],
                        annotation_text=f"{variable} = {sol_val:.3f}"
                    )
                    
                    # Add point at solution
                    fig.add_trace(go.Scatter(
                        x=[sol_val],
                        y=[0],
                        mode='markers',
                        name=f'Solution: {variable} = {sol_val:.3f}',
                        marker=dict(
                            size=10,
                            color=self.colors[i % len(self.colors)],
                            symbol='circle'
                        )
                    ))
            
            fig.update_layout(
                title=f'Equation: {sp.latex(expr)} = 0',
                xaxis_title=variable,
                yaxis_title='f(x)',
                template='plotly_white',
                showlegend=True,
                hovermode='x'
            )
            
            return {
                'figure': fig,
                'equation': equation,
                'solutions': solutions,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_system_of_equations(self, equations: List, variables: List[str] = ['x', 'y'],
                                ranges: Dict[str, Tuple[float, float]] = None) -> Dict[str, Any]:
        """
        Plot system of equations (2D only).
        
        Args:
            equations: List of SymPy equations
            variables: List of variable names
            ranges: Dictionary of variable ranges
            
        Returns:
            Dictionary containing plot figure
        """
        try:
            if len(variables) != 2:
                return {
                    'error': 'System visualization only supports 2 variables',
                    'success': False
                }
            
            x_var, y_var = variables[0], variables[1]
            
            # Set default ranges
            if ranges is None:
                ranges = {x_var: (-10, 10), y_var: (-10, 10)}
            
            x_range = ranges.get(x_var, (-10, 10))
            y_range = ranges.get(y_var, (-10, 10))
            
            # Create meshgrid
            x_vals = np.linspace(x_range[0], x_range[1], 400)
            y_vals = np.linspace(y_range[0], y_range[1], 400)
            X, Y = np.meshgrid(x_vals, y_vals)
            
            fig = go.Figure()
            
            # Plot each equation as a contour
            for i, eq in enumerate(equations):
                # Convert equation to expression
                if hasattr(eq, 'lhs') and hasattr(eq, 'rhs'):
                    expr = eq.lhs - eq.rhs
                else:
                    expr = eq
                
                # Convert to numerical function
                x_sym, y_sym = sp.Symbol(x_var), sp.Symbol(y_var)
                f = sp.lambdify([x_sym, y_sym], expr, 'numpy')
                
                # Calculate values
                Z = f(X, Y)
                
                # Add contour line at Z=0
                fig.add_trace(go.Contour(
                    x=x_vals,
                    y=y_vals,
                    z=Z,
                    contours=dict(
                        start=0,
                        end=0,
                        size=1,
                        coloring='lines'
                    ),
                    line=dict(
                        color=self.colors[i % len(self.colors)],
                        width=3
                    ),
                    name=f'Equation {i+1}: {sp.latex(expr)} = 0',
                    showscale=False
                ))
            
            fig.update_layout(
                title='System of Equations',
                xaxis_title=x_var,
                yaxis_title=y_var,
                template='plotly_white',
                showlegend=True
            )
            
            return {
                'figure': fig,
                'equations': equations,
                'variables': variables,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_inequality(self, inequality, variable: str = 'x',
                       x_range: Tuple[float, float] = (-10, 10)) -> Dict[str, Any]:
        """
        Plot inequality and shade the solution region.
        
        Args:
            inequality: SymPy inequality
            variable: Variable name
            x_range: Range for plotting
            
        Returns:
            Dictionary containing plot figure
        """
        try:
            # Extract left and right sides of inequality
            if hasattr(inequality, 'lhs') and hasattr(inequality, 'rhs'):
                left_expr = inequality.lhs
                right_expr = inequality.rhs
                relation = str(inequality.rel_op)
            else:
                return {
                    'error': 'Invalid inequality format',
                    'success': False
                }
            
            # Create x values
            x_vals = np.linspace(x_range[0], x_range[1], 1000)
            
            # Convert to numerical functions
            var = sp.Symbol(variable)
            f_left = sp.lambdify(var, left_expr, 'numpy')
            f_right = sp.lambdify(var, right_expr, 'numpy')
            
            # Calculate y values
            y_left = f_left(x_vals)
            y_right = f_right(x_vals)
            
            fig = go.Figure()
            
            # Plot both sides
            fig.add_trace(go.Scatter(
                x=x_vals,
                y=y_left,
                mode='lines',
                name=f'Left: {sp.latex(left_expr)}',
                line=dict(width=2, color='blue')
            ))
            
            fig.add_trace(go.Scatter(
                x=x_vals,
                y=y_right,
                mode='lines',
                name=f'Right: {sp.latex(right_expr)}',
                line=dict(width=2, color='red')
            ))
            
            # Shade solution region
            if relation in ['<', '<=']:
                # Shade where left < right
                fig.add_trace(go.Scatter(
                    x=x_vals,
                    y=np.minimum(y_left, y_right),
                    fill='tonexty',
                    mode='none',
                    name='Solution Region',
                    fillcolor='rgba(0, 255, 0, 0.2)'
                ))
            elif relation in ['>', '>=']:
                # Shade where left > right
                fig.add_trace(go.Scatter(
                    x=x_vals,
                    y=np.maximum(y_left, y_right),
                    fill='tonexty',
                    mode='none',
                    name='Solution Region',
                    fillcolor='rgba(0, 255, 0, 0.2)'
                ))
            
            fig.update_layout(
                title=f'Inequality: {sp.latex(left_expr)} {relation} {sp.latex(right_expr)}',
                xaxis_title=variable,
                yaxis_title='y',
                template='plotly_white',
                showlegend=True
            )
            
            return {
                'figure': fig,
                'inequality': inequality,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_polynomial_analysis(self, polynomial, variable: str = 'x',
                               x_range: Tuple[float, float] = (-10, 10)) -> Dict[str, Any]:
        """
        Create comprehensive polynomial analysis plot.
        
        Args:
            polynomial: SymPy polynomial expression
            variable: Variable name
            x_range: Range for plotting
            
        Returns:
            Dictionary containing subplot figure
        """
        try:
            var = sp.Symbol(variable)
            
            # Calculate derivatives
            first_derivative = sp.diff(polynomial, var)
            second_derivative = sp.diff(polynomial, var, 2)
            
            # Find critical points
            critical_points = sp.solve(first_derivative, var)
            critical_points = [float(cp.evalf()) for cp in critical_points if cp.is_real]
            
            # Find inflection points
            inflection_points = sp.solve(second_derivative, var)
            inflection_points = [float(ip.evalf()) for ip in inflection_points if ip.is_real]
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    f'f({variable}) = {sp.latex(polynomial)}',
                    f"f'({variable}) = {sp.latex(first_derivative)}",
                    f'f"({variable}) = {sp.latex(second_derivative)}',
                    'Critical & Inflection Points'
                )
            )
            
            # Create x values
            x_vals = np.linspace(x_range[0], x_range[1], 1000)
            
            # Convert to numerical functions
            f = sp.lambdify(var, polynomial, 'numpy')
            f_prime = sp.lambdify(var, first_derivative, 'numpy')
            f_double_prime = sp.lambdify(var, second_derivative, 'numpy')
            
            # Calculate y values
            y_vals = f(x_vals)
            y_prime_vals = f_prime(x_vals)
            y_double_prime_vals = f_double_prime(x_vals)
            
            # Plot original function
            fig.add_trace(
                go.Scatter(x=x_vals, y=y_vals, mode='lines', name='f(x)', line=dict(color='blue')),
                row=1, col=1
            )
            
            # Plot first derivative
            fig.add_trace(
                go.Scatter(x=x_vals, y=y_prime_vals, mode='lines', name="f'(x)", line=dict(color='red')),
                row=1, col=2
            )
            
            # Plot second derivative
            fig.add_trace(
                go.Scatter(x=x_vals, y=y_double_prime_vals, mode='lines', name='f"(x)', line=dict(color='green')),
                row=2, col=1
            )
            
            # Plot critical and inflection points
            fig.add_trace(
                go.Scatter(x=x_vals, y=y_vals, mode='lines', name='f(x)', line=dict(color='blue')),
                row=2, col=2
            )
            
            # Mark critical points
            for cp in critical_points:
                if x_range[0] <= cp <= x_range[1]:
                    y_cp = float(f(cp))
                    fig.add_trace(
                        go.Scatter(x=[cp], y=[y_cp], mode='markers', 
                                 marker=dict(size=10, color='red', symbol='circle'),
                                 name=f'Critical: ({cp:.2f}, {y_cp:.2f})'),
                        row=2, col=2
                    )
            
            # Mark inflection points
            for ip in inflection_points:
                if x_range[0] <= ip <= x_range[1]:
                    y_ip = float(f(ip))
                    fig.add_trace(
                        go.Scatter(x=[ip], y=[y_ip], mode='markers',
                                 marker=dict(size=10, color='orange', symbol='diamond'),
                                 name=f'Inflection: ({ip:.2f}, {y_ip:.2f})'),
                        row=2, col=2
                    )
            
            fig.update_layout(
                title_text='Polynomial Analysis',
                showlegend=False,
                template='plotly_white'
            )
            
            return {
                'figure': fig,
                'polynomial': polynomial,
                'critical_points': critical_points,
                'inflection_points': inflection_points,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
