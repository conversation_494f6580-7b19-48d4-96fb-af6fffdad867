"""
Function Plotter Module

Creates interactive plots for mathematical functions using <PERSON><PERSON><PERSON> and Mat<PERSON>lotlib.
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
import matplotlib.pyplot as plt
import sympy as sp
from typing import Dict, Any, List, Tuple, Optional, Union


class FunctionPlotter:
    """Interactive function plotting with <PERSON><PERSON><PERSON> and Matplotlib."""
    
    def __init__(self):
        self.default_range = (-10, 10)
        self.default_points = 1000
    
    def plot_function(self, expression, variable: str = 'x', 
                     x_range: Tuple[float, float] = None,
                     title: str = None, interactive: bool = True) -> Dict[str, Any]:
        """
        Plot a mathematical function.
        
        Args:
            expression: SymPy expression or string
            variable: Variable name (default 'x')
            x_range: Range for x-axis (default (-10, 10))
            title: Plot title
            interactive: Whether to create interactive plot
            
        Returns:
            Dictionary containing plot figure and metadata
        """
        try:
            # Parse expression if string
            if isinstance(expression, str):
                expr = sp.sympify(expression)
            else:
                expr = expression
            
            # Set up range and points
            if x_range is None:
                x_range = self.default_range
            
            x_vals = np.linspace(x_range[0], x_range[1], self.default_points)
            
            # Convert to numerical function
            var = sp.Symbol(variable)
            f = sp.lambdify(var, expr, 'numpy')
            
            # Calculate y values
            try:
                y_vals = f(x_vals)
                # Handle complex results
                if np.iscomplexobj(y_vals):
                    y_vals = np.real(y_vals)
            except Exception as e:
                # Handle domain errors by filtering valid points
                y_vals = []
                valid_x = []
                for x in x_vals:
                    try:
                        y = f(x)
                        if np.isfinite(y) and not np.iscomplex(y):
                            y_vals.append(y)
                            valid_x.append(x)
                    except:
                        continue
                x_vals = np.array(valid_x)
                y_vals = np.array(y_vals)
            
            if len(y_vals) == 0:
                return {
                    'success': False,
                    'error': 'No valid points to plot'
                }
            
            # Create plot
            if interactive:
                fig = self._create_plotly_figure(x_vals, y_vals, expr, title)
            else:
                fig = self._create_matplotlib_figure(x_vals, y_vals, expr, title)
            
            return {
                'figure': fig,
                'x_values': x_vals,
                'y_values': y_vals,
                'expression': expr,
                'latex': sp.latex(expr),
                'interactive': interactive,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_multiple_functions(self, expressions: List[Union[str, sp.Expr]], 
                              variable: str = 'x',
                              x_range: Tuple[float, float] = None,
                              labels: List[str] = None,
                              title: str = None) -> Dict[str, Any]:
        """
        Plot multiple functions on the same axes.
        
        Args:
            expressions: List of expressions to plot
            variable: Variable name
            x_range: Range for x-axis
            labels: Labels for each function
            title: Plot title
            
        Returns:
            Dictionary containing plot figure and metadata
        """
        try:
            if x_range is None:
                x_range = self.default_range
            
            x_vals = np.linspace(x_range[0], x_range[1], self.default_points)
            
            fig = go.Figure()
            
            plot_data = []
            
            for i, expr in enumerate(expressions):
                # Parse expression
                if isinstance(expr, str):
                    parsed_expr = sp.sympify(expr)
                else:
                    parsed_expr = expr
                
                # Convert to numerical function
                var = sp.Symbol(variable)
                f = sp.lambdify(var, parsed_expr, 'numpy')
                
                # Calculate y values
                try:
                    y_vals = f(x_vals)
                    if np.iscomplexobj(y_vals):
                        y_vals = np.real(y_vals)
                except:
                    # Handle domain errors
                    y_vals = []
                    valid_x = []
                    for x in x_vals:
                        try:
                            y = f(x)
                            if np.isfinite(y) and not np.iscomplex(y):
                                y_vals.append(y)
                                valid_x.append(x)
                        except:
                            continue
                    y_vals = np.array(y_vals)
                    valid_x = np.array(valid_x)
                else:
                    valid_x = x_vals
                
                # Determine label
                if labels and i < len(labels):
                    label = labels[i]
                else:
                    label = f'f_{i+1}(x) = {sp.latex(parsed_expr)}'
                
                # Add trace to figure
                fig.add_trace(go.Scatter(
                    x=valid_x,
                    y=y_vals,
                    mode='lines',
                    name=label,
                    line=dict(width=2)
                ))
                
                plot_data.append({
                    'expression': parsed_expr,
                    'x_values': valid_x,
                    'y_values': y_vals,
                    'label': label
                })
            
            # Update layout
            fig.update_layout(
                title=title or 'Multiple Functions',
                xaxis_title=variable,
                yaxis_title='f(x)',
                hovermode='x unified',
                showlegend=True,
                template='plotly_white'
            )
            
            return {
                'figure': fig,
                'plot_data': plot_data,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_derivative_comparison(self, expression, variable: str = 'x',
                                 x_range: Tuple[float, float] = None,
                                 orders: List[int] = [0, 1, 2]) -> Dict[str, Any]:
        """
        Plot function and its derivatives.
        
        Args:
            expression: Original function
            variable: Variable name
            x_range: Range for x-axis
            orders: List of derivative orders to plot (0 = original function)
            
        Returns:
            Dictionary containing plot figure and metadata
        """
        try:
            # Parse expression
            if isinstance(expression, str):
                expr = sp.sympify(expression)
            else:
                expr = expression
            
            var = sp.Symbol(variable)
            
            # Calculate derivatives
            derivatives = {}
            derivatives[0] = expr  # Original function
            
            for order in orders:
                if order > 0:
                    derivatives[order] = sp.diff(expr, var, order)
            
            # Create expressions list for plotting
            expressions = [derivatives[order] for order in sorted(orders)]
            labels = []
            
            for order in sorted(orders):
                if order == 0:
                    labels.append(f'f(x) = {sp.latex(expr)}')
                elif order == 1:
                    labels.append(f"f'(x) = {sp.latex(derivatives[order])}")
                elif order == 2:
                    labels.append(f'f"(x) = {sp.latex(derivatives[order])}')
                else:
                    labels.append(f'f^({order})(x) = {sp.latex(derivatives[order])}')
            
            # Plot multiple functions
            result = self.plot_multiple_functions(
                expressions, variable, x_range, labels,
                f'Function and Derivatives: {sp.latex(expr)}'
            )
            
            if result['success']:
                result['derivatives'] = derivatives
            
            return result
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_parametric(self, x_expr, y_expr, parameter: str = 't',
                       t_range: Tuple[float, float] = (0, 2*np.pi),
                       title: str = None) -> Dict[str, Any]:
        """
        Plot parametric equations.
        
        Args:
            x_expr: Expression for x(t)
            y_expr: Expression for y(t)
            parameter: Parameter name
            t_range: Range for parameter
            title: Plot title
            
        Returns:
            Dictionary containing plot figure and metadata
        """
        try:
            # Parse expressions
            if isinstance(x_expr, str):
                x_expr = sp.sympify(x_expr)
            if isinstance(y_expr, str):
                y_expr = sp.sympify(y_expr)
            
            # Create parameter values
            t_vals = np.linspace(t_range[0], t_range[1], self.default_points)
            
            # Convert to numerical functions
            t_var = sp.Symbol(parameter)
            x_func = sp.lambdify(t_var, x_expr, 'numpy')
            y_func = sp.lambdify(t_var, y_expr, 'numpy')
            
            # Calculate values
            x_vals = x_func(t_vals)
            y_vals = y_func(t_vals)
            
            # Create plot
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=x_vals,
                y=y_vals,
                mode='lines',
                name=f'x = {sp.latex(x_expr)}, y = {sp.latex(y_expr)}',
                line=dict(width=2)
            ))
            
            fig.update_layout(
                title=title or f'Parametric Plot: x({parameter}) = {sp.latex(x_expr)}, y({parameter}) = {sp.latex(y_expr)}',
                xaxis_title='x',
                yaxis_title='y',
                template='plotly_white',
                showlegend=True
            )
            
            return {
                'figure': fig,
                'x_expression': x_expr,
                'y_expression': y_expr,
                'x_values': x_vals,
                'y_values': y_vals,
                't_values': t_vals,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def _create_plotly_figure(self, x_vals: np.ndarray, y_vals: np.ndarray,
                            expression, title: str = None) -> go.Figure:
        """Create interactive Plotly figure."""
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=x_vals,
            y=y_vals,
            mode='lines',
            name=f'f(x) = {sp.latex(expression)}',
            line=dict(width=2, color='blue'),
            hovertemplate='x: %{x:.3f}<br>y: %{y:.3f}<extra></extra>'
        ))
        
        fig.update_layout(
            title=title or f'Plot of f(x) = {sp.latex(expression)}',
            xaxis_title='x',
            yaxis_title='f(x)',
            template='plotly_white',
            hovermode='x',
            showlegend=True
        )
        
        # Add grid
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        
        return fig
    
    def _create_matplotlib_figure(self, x_vals: np.ndarray, y_vals: np.ndarray,
                                expression, title: str = None) -> plt.Figure:
        """Create static Matplotlib figure."""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        ax.plot(x_vals, y_vals, 'b-', linewidth=2, label=f'f(x) = ${sp.latex(expression)}$')
        
        ax.set_xlabel('x')
        ax.set_ylabel('f(x)')
        ax.set_title(title or f'Plot of f(x) = ${sp.latex(expression)}$')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        return fig
    
    def create_interactive_slider_plot(self, expression_template: str, 
                                     parameter: str, param_range: Tuple[float, float],
                                     variable: str = 'x') -> Dict[str, Any]:
        """
        Create plot with interactive parameter slider.
        
        Args:
            expression_template: Expression with parameter (e.g., 'a*x**2 + b*x + c')
            parameter: Parameter to vary with slider
            param_range: Range for parameter
            variable: Independent variable
            
        Returns:
            Dictionary containing interactive plot
        """
        try:
            # This would be implemented with Plotly's slider functionality
            # For now, return a basic implementation
            
            param_vals = np.linspace(param_range[0], param_range[1], 20)
            x_range = self.default_range
            x_vals = np.linspace(x_range[0], x_range[1], self.default_points)
            
            fig = go.Figure()
            
            # Add traces for different parameter values
            for i, param_val in enumerate(param_vals):
                # Substitute parameter value
                expr_str = expression_template.replace(parameter, str(param_val))
                expr = sp.sympify(expr_str)
                
                # Convert to numerical function
                var = sp.Symbol(variable)
                f = sp.lambdify(var, expr, 'numpy')
                y_vals = f(x_vals)
                
                visible = True if i == 0 else False
                
                fig.add_trace(go.Scatter(
                    x=x_vals,
                    y=y_vals,
                    mode='lines',
                    name=f'{parameter} = {param_val:.2f}',
                    visible=visible
                ))
            
            # Create slider steps
            steps = []
            for i, param_val in enumerate(param_vals):
                step = dict(
                    method="update",
                    args=[{"visible": [False] * len(param_vals)},
                          {"title": f"Plot with {parameter} = {param_val:.2f}"}],
                    label=f"{param_val:.2f}"
                )
                step["args"][0]["visible"][i] = True
                steps.append(step)
            
            # Add slider
            sliders = [dict(
                active=0,
                currentvalue={"prefix": f"{parameter}: "},
                pad={"t": 50},
                steps=steps
            )]
            
            fig.update_layout(
                sliders=sliders,
                title=f"Interactive Plot: {expression_template}",
                xaxis_title=variable,
                yaxis_title='f(x)',
                template='plotly_white'
            )
            
            return {
                'figure': fig,
                'parameter': parameter,
                'param_range': param_range,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
