"""
Statistics Visualizer Module

Creates visualizations for statistical data, distributions, and analysis results.
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
import plotly.figure_factory as ff
from plotly.subplots import make_subplots
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
from scipy import stats


class StatisticsVisualizer:
    """Visualizer for statistical data and distributions."""
    
    def __init__(self):
        self.colors = px.colors.qualitative.Set1
    
    def plot_histogram(self, data: List[float], bins: int = 30, 
                      title: str = None, show_stats: bool = True) -> Dict[str, Any]:
        """
        Create histogram with optional statistical overlays.
        
        Args:
            data: List of numerical data
            bins: Number of bins
            title: Plot title
            show_stats: Whether to show mean, median lines
            
        Returns:
            Dictionary containing plot figure and statistics
        """
        try:
            data_array = np.array(data)
            
            # Create histogram
            fig = go.Figure()
            
            fig.add_trace(go.Histogram(
                x=data_array,
                nbinsx=bins,
                name='Data',
                opacity=0.7,
                marker_color='lightblue'
            ))
            
            if show_stats:
                mean_val = np.mean(data_array)
                median_val = np.median(data_array)
                
                # Add mean line
                fig.add_vline(
                    x=mean_val,
                    line_dash="dash",
                    line_color="red",
                    annotation_text=f"Mean: {mean_val:.2f}"
                )
                
                # Add median line
                fig.add_vline(
                    x=median_val,
                    line_dash="dot",
                    line_color="green",
                    annotation_text=f"Median: {median_val:.2f}"
                )
            
            fig.update_layout(
                title=title or 'Data Distribution',
                xaxis_title='Value',
                yaxis_title='Frequency',
                template='plotly_white',
                showlegend=True
            )
            
            # Calculate statistics
            statistics = {
                'mean': np.mean(data_array),
                'median': np.median(data_array),
                'std_dev': np.std(data_array, ddof=1),
                'variance': np.var(data_array, ddof=1),
                'min': np.min(data_array),
                'max': np.max(data_array),
                'count': len(data_array)
            }
            
            return {
                'figure': fig,
                'statistics': statistics,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_box_plot(self, data: List[float], title: str = None) -> Dict[str, Any]:
        """
        Create box plot for data distribution analysis.
        
        Args:
            data: List of numerical data
            title: Plot title
            
        Returns:
            Dictionary containing plot figure and quartile information
        """
        try:
            data_array = np.array(data)
            
            fig = go.Figure()
            
            fig.add_trace(go.Box(
                y=data_array,
                name='Data',
                boxpoints='outliers',
                marker_color='lightblue'
            ))
            
            fig.update_layout(
                title=title or 'Box Plot',
                yaxis_title='Value',
                template='plotly_white'
            )
            
            # Calculate quartiles
            quartiles = {
                'Q1': np.percentile(data_array, 25),
                'Q2': np.percentile(data_array, 50),  # Median
                'Q3': np.percentile(data_array, 75),
                'IQR': np.percentile(data_array, 75) - np.percentile(data_array, 25)
            }
            
            return {
                'figure': fig,
                'quartiles': quartiles,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_probability_distribution(self, dist_type: str, parameters: Dict[str, float],
                                    x_range: Tuple[float, float] = None,
                                    title: str = None) -> Dict[str, Any]:
        """
        Plot probability distribution function.
        
        Args:
            dist_type: Type of distribution ('normal', 'binomial', 'poisson', etc.)
            parameters: Distribution parameters
            x_range: Range for x-axis
            title: Plot title
            
        Returns:
            Dictionary containing plot figure
        """
        try:
            fig = go.Figure()
            
            if dist_type.lower() == 'normal':
                mean = parameters.get('mean', 0)
                std_dev = parameters.get('std_dev', 1)
                
                if x_range is None:
                    x_range = (mean - 4*std_dev, mean + 4*std_dev)
                
                x_vals = np.linspace(x_range[0], x_range[1], 1000)
                y_vals = stats.norm.pdf(x_vals, mean, std_dev)
                
                fig.add_trace(go.Scatter(
                    x=x_vals,
                    y=y_vals,
                    mode='lines',
                    name=f'Normal(μ={mean}, σ={std_dev})',
                    fill='tonexty'
                ))
                
                fig.update_layout(
                    title=title or f'Normal Distribution (μ={mean}, σ={std_dev})',
                    xaxis_title='x',
                    yaxis_title='Probability Density'
                )
                
            elif dist_type.lower() == 'binomial':
                n = parameters.get('n', 10)
                p = parameters.get('p', 0.5)
                
                x_vals = np.arange(0, n + 1)
                y_vals = stats.binom.pmf(x_vals, n, p)
                
                fig.add_trace(go.Bar(
                    x=x_vals,
                    y=y_vals,
                    name=f'Binomial(n={n}, p={p})'
                ))
                
                fig.update_layout(
                    title=title or f'Binomial Distribution (n={n}, p={p})',
                    xaxis_title='k',
                    yaxis_title='Probability'
                )
                
            elif dist_type.lower() == 'poisson':
                lambda_param = parameters.get('lambda', 1)
                
                x_vals = np.arange(0, int(lambda_param * 4) + 1)
                y_vals = stats.poisson.pmf(x_vals, lambda_param)
                
                fig.add_trace(go.Bar(
                    x=x_vals,
                    y=y_vals,
                    name=f'Poisson(λ={lambda_param})'
                ))
                
                fig.update_layout(
                    title=title or f'Poisson Distribution (λ={lambda_param})',
                    xaxis_title='k',
                    yaxis_title='Probability'
                )
            
            fig.update_layout(
                template='plotly_white',
                showlegend=True
            )
            
            return {
                'figure': fig,
                'distribution': dist_type,
                'parameters': parameters,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_scatter_with_regression(self, x_data: List[float], y_data: List[float],
                                   title: str = None, show_regression: bool = True) -> Dict[str, Any]:
        """
        Create scatter plot with optional regression line.
        
        Args:
            x_data: X coordinates
            y_data: Y coordinates
            title: Plot title
            show_regression: Whether to show regression line
            
        Returns:
            Dictionary containing plot figure and regression statistics
        """
        try:
            x_array = np.array(x_data)
            y_array = np.array(y_data)
            
            fig = go.Figure()
            
            # Add scatter plot
            fig.add_trace(go.Scatter(
                x=x_array,
                y=y_array,
                mode='markers',
                name='Data Points',
                marker=dict(size=8, color='blue', opacity=0.6)
            ))
            
            regression_stats = {}
            
            if show_regression:
                # Calculate regression line
                slope, intercept, r_value, p_value, std_err = stats.linregress(x_array, y_array)
                
                # Create regression line
                x_line = np.linspace(np.min(x_array), np.max(x_array), 100)
                y_line = slope * x_line + intercept
                
                fig.add_trace(go.Scatter(
                    x=x_line,
                    y=y_line,
                    mode='lines',
                    name=f'Regression Line (R² = {r_value**2:.3f})',
                    line=dict(color='red', width=2)
                ))
                
                regression_stats = {
                    'slope': slope,
                    'intercept': intercept,
                    'r_squared': r_value**2,
                    'correlation': r_value,
                    'p_value': p_value,
                    'std_error': std_err
                }
            
            fig.update_layout(
                title=title or 'Scatter Plot with Regression',
                xaxis_title='X',
                yaxis_title='Y',
                template='plotly_white',
                showlegend=True
            )
            
            return {
                'figure': fig,
                'regression_stats': regression_stats,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def plot_confidence_interval(self, data: List[float], confidence_level: float = 0.95,
                               title: str = None) -> Dict[str, Any]:
        """
        Plot data with confidence interval.
        
        Args:
            data: List of numerical data
            confidence_level: Confidence level (e.g., 0.95 for 95%)
            title: Plot title
            
        Returns:
            Dictionary containing plot figure and interval information
        """
        try:
            data_array = np.array(data)
            n = len(data_array)
            mean = np.mean(data_array)
            std_err = stats.sem(data_array)
            
            # Calculate confidence interval
            alpha = 1 - confidence_level
            t_critical = stats.t.ppf(1 - alpha/2, n - 1)
            margin_error = t_critical * std_err
            
            ci_lower = mean - margin_error
            ci_upper = mean + margin_error
            
            fig = go.Figure()
            
            # Add data points
            fig.add_trace(go.Scatter(
                x=list(range(len(data_array))),
                y=data_array,
                mode='markers',
                name='Data Points',
                marker=dict(size=8, color='blue', opacity=0.6)
            ))
            
            # Add mean line
            fig.add_hline(
                y=mean,
                line_dash="dash",
                line_color="red",
                annotation_text=f"Mean: {mean:.3f}"
            )
            
            # Add confidence interval
            fig.add_hrect(
                y0=ci_lower,
                y1=ci_upper,
                fillcolor="rgba(255, 0, 0, 0.2)",
                layer="below",
                line_width=0,
                annotation_text=f"{confidence_level*100}% CI"
            )
            
            fig.update_layout(
                title=title or f'{confidence_level*100}% Confidence Interval',
                xaxis_title='Observation',
                yaxis_title='Value',
                template='plotly_white',
                showlegend=True
            )
            
            interval_info = {
                'mean': mean,
                'std_error': std_err,
                'margin_error': margin_error,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'confidence_level': confidence_level
            }
            
            return {
                'figure': fig,
                'interval_info': interval_info,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def create_statistical_summary_plot(self, data: List[float], 
                                      title: str = None) -> Dict[str, Any]:
        """
        Create comprehensive statistical summary with multiple subplots.
        
        Args:
            data: List of numerical data
            title: Overall title
            
        Returns:
            Dictionary containing subplot figure
        """
        try:
            data_array = np.array(data)
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Histogram', 'Box Plot', 'Q-Q Plot', 'Statistics'),
                specs=[[{"type": "xy"}, {"type": "xy"}],
                       [{"type": "xy"}, {"type": "table"}]]
            )
            
            # Histogram
            fig.add_trace(
                go.Histogram(x=data_array, nbinsx=20, name='Distribution'),
                row=1, col=1
            )
            
            # Box plot
            fig.add_trace(
                go.Box(y=data_array, name='Box Plot'),
                row=1, col=2
            )
            
            # Q-Q plot
            theoretical_quantiles = stats.norm.ppf(np.linspace(0.01, 0.99, len(data_array)))
            sample_quantiles = np.sort(data_array)
            
            fig.add_trace(
                go.Scatter(
                    x=theoretical_quantiles,
                    y=sample_quantiles,
                    mode='markers',
                    name='Q-Q Plot'
                ),
                row=2, col=1
            )
            
            # Add reference line for Q-Q plot
            min_val = min(np.min(theoretical_quantiles), np.min(sample_quantiles))
            max_val = max(np.max(theoretical_quantiles), np.max(sample_quantiles))
            fig.add_trace(
                go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    line=dict(dash='dash', color='red'),
                    name='Reference Line'
                ),
                row=2, col=1
            )
            
            # Statistics table
            stats_data = {
                'Statistic': ['Mean', 'Median', 'Std Dev', 'Variance', 'Min', 'Max', 'Count'],
                'Value': [
                    f'{np.mean(data_array):.3f}',
                    f'{np.median(data_array):.3f}',
                    f'{np.std(data_array, ddof=1):.3f}',
                    f'{np.var(data_array, ddof=1):.3f}',
                    f'{np.min(data_array):.3f}',
                    f'{np.max(data_array):.3f}',
                    f'{len(data_array)}'
                ]
            }
            
            fig.add_trace(
                go.Table(
                    header=dict(values=list(stats_data.keys())),
                    cells=dict(values=list(stats_data.values()))
                ),
                row=2, col=2
            )
            
            fig.update_layout(
                title_text=title or 'Statistical Summary',
                showlegend=False,
                template='plotly_white'
            )
            
            return {
                'figure': fig,
                'statistics': stats_data,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
