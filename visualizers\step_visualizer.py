"""
Step Visualizer Module

Creates visual representations of solution steps and mathematical transformations.
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import sympy as sp
import numpy as np
from typing import Dict, Any, List, Tuple, Optional


class StepVisualizer:
    """Visualizer for step-by-step solution processes."""
    
    def __init__(self):
        self.colors = px.colors.qualitative.Set1
        self.step_colors = {
            'setup': '#3498db',      # Blue
            'transformation': '#e74c3c',  # Red
            'solution': '#2ecc71',   # Green
            'calculus': '#9b59b6',   # Purple
            'statistics': '#f39c12', # Orange
            'general': '#34495e'     # Dark gray
        }
    
    def create_step_flow_diagram(self, steps: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create a flow diagram showing the solution process.
        
        Args:
            steps: List of solution steps
            
        Returns:
            Dictionary containing flow diagram figure
        """
        try:
            if not steps:
                return {'error': 'No steps provided', 'success': False}
            
            # Create a simple flow diagram using scatter plot
            fig = go.Figure()
            
            # Position steps vertically
            y_positions = list(range(len(steps)))
            x_positions = [0] * len(steps)
            
            # Add step nodes
            for i, step in enumerate(steps):
                step_type = step.get('step_type', 'general')
                color = self.step_colors.get(step_type, self.step_colors['general'])
                
                fig.add_trace(go.Scatter(
                    x=[x_positions[i]],
                    y=[y_positions[i]],
                    mode='markers+text',
                    marker=dict(
                        size=30,
                        color=color,
                        symbol='circle'
                    ),
                    text=[f"Step {i+1}"],
                    textposition="middle center",
                    textfont=dict(color='white', size=12),
                    name=step.get('title', f'Step {i+1}'),
                    hovertemplate=f"<b>{step.get('title', f'Step {i+1}')}</b><br>" +
                                f"{step.get('explanation', '')}<extra></extra>"
                ))
            
            # Add arrows between steps
            for i in range(len(steps) - 1):
                fig.add_annotation(
                    x=x_positions[i],
                    y=y_positions[i] - 0.3,
                    ax=x_positions[i+1],
                    ay=y_positions[i+1] + 0.3,
                    arrowhead=2,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor='gray'
                )
            
            fig.update_layout(
                title='Solution Process Flow',
                xaxis=dict(visible=False),
                yaxis=dict(
                    tickmode='array',
                    tickvals=y_positions,
                    ticktext=[step.get('title', f'Step {i+1}') for i, step in enumerate(steps)],
                    autorange='reversed'
                ),
                showlegend=False,
                template='plotly_white',
                height=max(400, len(steps) * 80)
            )
            
            return {
                'figure': fig,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def create_transformation_animation(self, steps: List[Dict[str, Any]], 
                                      variable: str = 'x') -> Dict[str, Any]:
        """
        Create animated visualization of mathematical transformations.
        
        Args:
            steps: List of solution steps with expressions
            variable: Variable to plot
            
        Returns:
            Dictionary containing animated figure
        """
        try:
            # Filter steps that have expressions we can plot
            plottable_steps = []
            for step in steps:
                if 'expression' in step and step['expression'] is not None:
                    try:
                        expr = step['expression']
                        if hasattr(expr, 'free_symbols'):
                            plottable_steps.append(step)
                    except:
                        continue
            
            if not plottable_steps:
                return {'error': 'No plottable expressions found', 'success': False}
            
            # Create x values for plotting
            x_vals = np.linspace(-10, 10, 1000)
            var = sp.Symbol(variable)
            
            # Create frames for animation
            frames = []
            
            for i, step in enumerate(plottable_steps):
                expr = step['expression']
                
                try:
                    # Convert to numerical function
                    f = sp.lambdify(var, expr, 'numpy')
                    y_vals = f(x_vals)
                    
                    # Handle complex results
                    if np.iscomplexobj(y_vals):
                        y_vals = np.real(y_vals)
                    
                    frame = go.Frame(
                        data=[go.Scatter(
                            x=x_vals,
                            y=y_vals,
                            mode='lines',
                            name=step.get('title', f'Step {i+1}'),
                            line=dict(width=3, color=self.colors[i % len(self.colors)])
                        )],
                        name=f'Step {i+1}',
                        layout=go.Layout(
                            title=f"Step {i+1}: {step.get('title', '')}"
                        )
                    )
                    frames.append(frame)
                    
                except Exception as e:
                    continue
            
            if not frames:
                return {'error': 'Could not create animation frames', 'success': False}
            
            # Create initial figure
            fig = go.Figure(
                data=frames[0].data,
                frames=frames
            )
            
            # Add animation controls
            fig.update_layout(
                title='Mathematical Transformation Animation',
                xaxis_title=variable,
                yaxis_title='f(x)',
                template='plotly_white',
                updatemenus=[{
                    'type': 'buttons',
                    'showactive': False,
                    'buttons': [
                        {
                            'label': 'Play',
                            'method': 'animate',
                            'args': [None, {
                                'frame': {'duration': 1000, 'redraw': True},
                                'fromcurrent': True,
                                'transition': {'duration': 300}
                            }]
                        },
                        {
                            'label': 'Pause',
                            'method': 'animate',
                            'args': [[None], {
                                'frame': {'duration': 0, 'redraw': False},
                                'mode': 'immediate',
                                'transition': {'duration': 0}
                            }]
                        }
                    ]
                }],
                sliders=[{
                    'steps': [
                        {
                            'args': [[f'Step {i+1}'], {
                                'frame': {'duration': 300, 'redraw': True},
                                'mode': 'immediate',
                                'transition': {'duration': 300}
                            }],
                            'label': f'Step {i+1}',
                            'method': 'animate'
                        }
                        for i in range(len(frames))
                    ],
                    'active': 0,
                    'currentvalue': {'prefix': 'Current: '},
                    'len': 0.9,
                    'x': 0.1,
                    'xanchor': 'left',
                    'y': 0,
                    'yanchor': 'top'
                }]
            )
            
            return {
                'figure': fig,
                'frames': len(frames),
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def create_step_comparison_plot(self, steps: List[Dict[str, Any]], 
                                  variable: str = 'x') -> Dict[str, Any]:
        """
        Create side-by-side comparison of expressions at different steps.
        
        Args:
            steps: List of solution steps
            variable: Variable to plot
            
        Returns:
            Dictionary containing comparison plot
        """
        try:
            # Filter plottable steps
            plottable_steps = []
            for step in steps:
                if 'expression' in step and step['expression'] is not None:
                    try:
                        expr = step['expression']
                        if hasattr(expr, 'free_symbols'):
                            plottable_steps.append(step)
                    except:
                        continue
            
            if len(plottable_steps) < 2:
                return {'error': 'Need at least 2 plottable steps for comparison', 'success': False}
            
            # Create subplots
            n_steps = len(plottable_steps)
            cols = min(3, n_steps)
            rows = (n_steps + cols - 1) // cols
            
            fig = make_subplots(
                rows=rows,
                cols=cols,
                subplot_titles=[step.get('title', f'Step {i+1}') 
                              for i, step in enumerate(plottable_steps)]
            )
            
            # Create x values
            x_vals = np.linspace(-10, 10, 500)
            var = sp.Symbol(variable)
            
            # Plot each step
            for i, step in enumerate(plottable_steps):
                row = i // cols + 1
                col = i % cols + 1
                
                expr = step['expression']
                
                try:
                    # Convert to numerical function
                    f = sp.lambdify(var, expr, 'numpy')
                    y_vals = f(x_vals)
                    
                    # Handle complex results
                    if np.iscomplexobj(y_vals):
                        y_vals = np.real(y_vals)
                    
                    fig.add_trace(
                        go.Scatter(
                            x=x_vals,
                            y=y_vals,
                            mode='lines',
                            name=f'Step {i+1}',
                            line=dict(width=2, color=self.colors[i % len(self.colors)]),
                            showlegend=False
                        ),
                        row=row,
                        col=col
                    )
                    
                except Exception as e:
                    # Add error message as annotation
                    fig.add_annotation(
                        text=f"Cannot plot: {str(e)[:50]}...",
                        x=0.5,
                        y=0.5,
                        xref=f'x{i+1}',
                        yref=f'y{i+1}',
                        showarrow=False,
                        row=row,
                        col=col
                    )
            
            fig.update_layout(
                title='Step-by-Step Comparison',
                template='plotly_white',
                height=300 * rows
            )
            
            # Update all subplot axes
            for i in range(1, n_steps + 1):
                fig.update_xaxes(title_text=variable, row=(i-1)//cols + 1, col=(i-1)%cols + 1)
                fig.update_yaxes(title_text='f(x)', row=(i-1)//cols + 1, col=(i-1)%cols + 1)
            
            return {
                'figure': fig,
                'steps_plotted': len(plottable_steps),
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def create_step_summary_table(self, steps: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create a summary table of all solution steps.
        
        Args:
            steps: List of solution steps
            
        Returns:
            Dictionary containing table figure
        """
        try:
            if not steps:
                return {'error': 'No steps provided', 'success': False}
            
            # Prepare table data
            step_numbers = [f"Step {i+1}" for i in range(len(steps))]
            step_titles = [step.get('title', f'Step {i+1}') for i, step in enumerate(steps)]
            step_explanations = [step.get('explanation', 'No explanation') for step in steps]
            step_expressions = []
            
            for step in steps:
                if 'expression' in step and step['expression'] is not None:
                    try:
                        expr_str = str(step['expression'])
                        if len(expr_str) > 50:
                            expr_str = expr_str[:47] + "..."
                        step_expressions.append(expr_str)
                    except:
                        step_expressions.append("Complex expression")
                else:
                    step_expressions.append("No expression")
            
            # Create table
            fig = go.Figure(data=[go.Table(
                header=dict(
                    values=['Step', 'Title', 'Expression', 'Explanation'],
                    fill_color='lightblue',
                    align='left',
                    font=dict(size=12, color='black')
                ),
                cells=dict(
                    values=[step_numbers, step_titles, step_expressions, step_explanations],
                    fill_color='white',
                    align='left',
                    font=dict(size=11, color='black'),
                    height=30
                )
            )])
            
            fig.update_layout(
                title='Solution Steps Summary',
                template='plotly_white',
                height=max(300, len(steps) * 40 + 100)
            )
            
            return {
                'figure': fig,
                'step_count': len(steps),
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
